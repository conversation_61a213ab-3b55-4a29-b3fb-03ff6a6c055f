<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\LunchController;
use App\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return redirect()->route('lunch.index');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');

    // Lunch Management Routes
    Route::prefix('lunch')->name('lunch.')->group(function () {
        Route::get('/', [LunchController::class, 'index'])->name('index');
        Route::post('/place-order', [LunchController::class, 'placeOrder'])->name('place-order');
        Route::post('/cancel-order', [LunchController::class, 'cancelOrder'])->name('cancel-order');
        Route::get('/history', [LunchController::class, 'history'])->name('history');
        Route::get('/upcoming', [LunchController::class, 'upcoming'])->name('upcoming');
        Route::post('/place-order-for-date', [LunchController::class, 'placeOrderForDate'])->name('place-order-for-date');
        Route::post('/cancel-order-for-date', [LunchController::class, 'cancelOrderForDate'])->name('cancel-order-for-date');
        Route::post('/place-tomorrow-order', [LunchController::class, 'placeTomorrowOrder'])->name('place-tomorrow-order');
        Route::post('/cancel-tomorrow-order', [LunchController::class, 'cancelTomorrowOrder'])->name('cancel-tomorrow-order');
        Route::get('/today-summary', [LunchController::class, 'todaySummary'])->name('today-summary');
    });

    // Admin Routes
    Route::middleware('admin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/', [AdminController::class, 'index'])->name('index');
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::post('/users', [AdminController::class, 'createUser'])->name('users.create');
        Route::get('/users/{user}/edit', [AdminController::class, 'editUser'])->name('users.edit');
        Route::put('/users/{user}', [AdminController::class, 'updateUser'])->name('users.update');
        Route::patch('/users/{user}/mode', [AdminController::class, 'updateUserMode'])->name('users.update-mode');
        Route::patch('/users/{user}/status', [AdminController::class, 'updateUserStatus'])->name('users.update-status');
        Route::get('/lunch-orders', [AdminController::class, 'lunchOrders'])->name('lunch-orders');
        Route::get('/orders', [AdminController::class, 'lunchOrders'])->name('orders');
        Route::get('/pricing', [AdminController::class, 'pricing'])->name('pricing');
        Route::post('/pricing', [AdminController::class, 'setPrice'])->name('pricing.set');
        Route::get('/billing', [AdminController::class, 'billing'])->name('billing');
        Route::post('/billing/generate', [AdminController::class, 'generateBills'])->name('billing.generate');
        Route::get('/business-days', [AdminController::class, 'businessDays'])->name('business-days');
        Route::post('/business-days/create-auto-orders', [AdminController::class, 'createMonthlyAutoOrders'])->name('business-days.create-auto-orders');
        Route::get('/weekend-holiday-config', [AdminController::class, 'weekendHolidayConfig'])->name('weekend-holiday-config');
        Route::get('/weekend-config', [AdminController::class, 'weekendConfig'])->name('weekend-config');
        Route::post('/weekend-config/set', [AdminController::class, 'setWeekendConfig'])->name('weekend-config.set');
        Route::post('/weekend-config/bulk-set', [AdminController::class, 'setBulkWeekendConfig'])->name('weekend-config.bulk-set');
        Route::post('/holiday/add', [AdminController::class, 'addHoliday'])->name('holiday.add');
        Route::post('/holiday/remove', [AdminController::class, 'removeHoliday'])->name('holiday.remove');
        Route::post('/holiday/bulk-import', [AdminController::class, 'bulkImportHolidays'])->name('holiday.bulk-import');
        Route::post('/weekend-config/validate', [AdminController::class, 'validateWeekendConfig'])->name('weekend-config.validate');
        Route::post('/holiday/validate', [AdminController::class, 'validateHolidayConfig'])->name('holiday.validate');
        Route::post('/debug-bulk-config', [AdminController::class, 'debugBulkConfig'])->name('debug-bulk-config');
    });
});

require __DIR__.'/auth.php';
