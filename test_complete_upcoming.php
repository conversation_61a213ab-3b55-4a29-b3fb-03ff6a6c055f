<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\LunchOrderService;
use App\Models\User;
use Carbon\Carbon;

try {
    echo "Testing complete upcoming orders functionality...\n";
    
    $lunchOrderService = app(LunchOrderService::class);
    
    // Get a test user
    $user = User::where('role', 'user')->first();
    if (!$user) {
        echo "No user found!\n";
        exit;
    }
    
    echo "Testing with user: " . $user->email . "\n";
    
    // Test upcoming orders summary
    echo "\nTesting getUpcomingOrdersSummary...\n";
    $summary = $lunchOrderService->getUpcomingOrdersSummary($user);
    
    echo "Upcoming Orders Summary:\n";
    echo "- Total orders: " . $summary['total'] . "\n";
    echo "- Ordered: " . $summary['ordered'] . "\n";
    echo "- Cancelled: " . $summary['cancelled'] . "\n";
    echo "- Cancellable: " . $summary['cancellable'] . "\n";
    echo "- Business days: " . $summary['business_days'] . "\n";
    
    if ($summary['next_order_date']) {
        echo "- Next order date: " . $summary['next_order_date']->format('Y-m-d l') . "\n";
    } else {
        echo "- Next order date: None\n";
    }
    
    // Test cancellable count
    echo "\nTesting getCancellableUpcomingOrdersCount...\n";
    $cancellableCount = $lunchOrderService->getCancellableUpcomingOrdersCount($user);
    echo "Cancellable upcoming orders: " . $cancellableCount . "\n";
    
    // Test placing an order for a future date
    echo "\nTesting placing order for future date...\n";
    $futureDate = Carbon::now()->addDays(5);
    
    try {
        $order = $lunchOrderService->placeOrder($user, $futureDate);
        echo "✓ Successfully placed order for: " . $futureDate->format('Y-m-d l') . "\n";
        
        // Test cancelling the future order
        echo "\nTesting cancelling future order...\n";
        $cancelled = $lunchOrderService->cancelOrder($user, $futureDate);
        if ($cancelled) {
            echo "✓ Successfully cancelled order for: " . $futureDate->format('Y-m-d l') . "\n";
        } else {
            echo "✗ Failed to cancel order\n";
        }
        
    } catch (Exception $e) {
        echo "✗ Failed to place future order: " . $e->getMessage() . "\n";
    }
    
    // Test cancellation logic for different scenarios
    echo "\nTesting cancellation logic for different scenarios...\n";

    $testDates = [
        ['date' => Carbon::yesterday(), 'description' => 'Yesterday (past)'],
        ['date' => Carbon::today(), 'description' => 'Today'],
        ['date' => Carbon::tomorrow(), 'description' => 'Tomorrow (future)'],
        ['date' => Carbon::now()->addWeek(), 'description' => 'Next week (future)']
    ];

    foreach ($testDates as $test) {
        $cancelStatus = $lunchOrderService->canCancelOrder($user, $test['date']);
        echo "- {$test['description']}: " . ($cancelStatus['can_cancel'] ? '✓ Can cancel' : '✗ Cannot cancel') . " - " . $cancelStatus['reason'] . "\n";
    }
    
    echo "\nTest completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
