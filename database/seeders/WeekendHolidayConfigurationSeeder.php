<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\WeekendConfiguration;
use App\Models\HolidayConfiguration;
use Carbon\Carbon;

class WeekendHolidayConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default weekend configurations for current and next year
        $currentYear = Carbon::now()->year;
        $nextYear = $currentYear + 1;

        // Default weekend configuration (Saturday and Sunday)
        for ($year = $currentYear; $year <= $nextYear; $year++) {
            for ($month = 1; $month <= 12; $month++) {
                WeekendConfiguration::updateOrCreate(
                    ['year' => $year, 'month' => $month],
                    [
                        'weekend_days' => [0, 6], // Sunday and Saturday
                        'description' => 'Default weekend configuration',
                        'is_active' => true,
                    ]
                );
            }
        }

        // Create some sample holiday configurations
        $holidays = [
            // Current year holidays
            [
                'date' => $currentYear . '-01-01',
                'name' => 'New Year\'s Day',
                'description' => 'First day of the year',
                'type' => 'fixed'
            ],
            [
                'date' => $currentYear . '-07-04',
                'name' => 'Independence Day',
                'description' => 'National independence day',
                'type' => 'fixed'
            ],
            [
                'date' => $currentYear . '-12-25',
                'name' => 'Christmas Day',
                'description' => 'Christmas celebration',
                'type' => 'fixed'
            ],
            
            // Next year holidays
            [
                'date' => $nextYear . '-01-01',
                'name' => 'New Year\'s Day',
                'description' => 'First day of the year',
                'type' => 'fixed'
            ],
            [
                'date' => $nextYear . '-07-04',
                'name' => 'Independence Day',
                'description' => 'National independence day',
                'type' => 'fixed'
            ],
            [
                'date' => $nextYear . '-12-25',
                'name' => 'Christmas Day',
                'description' => 'Christmas celebration',
                'type' => 'fixed'
            ],
        ];

        foreach ($holidays as $holiday) {
            $date = Carbon::parse($holiday['date']);
            
            HolidayConfiguration::updateOrCreate(
                [
                    'holiday_date' => $date->format('Y-m-d'),
                    'year' => $date->year,
                    'month' => $date->month,
                ],
                [
                    'name' => $holiday['name'],
                    'description' => $holiday['description'],
                    'type' => $holiday['type'],
                    'is_active' => true,
                ]
            );
        }

        // Create some custom weekend configurations for special months
        // Example: Different weekend for December (maybe Friday-Saturday for holiday season)
        WeekendConfiguration::updateOrCreate(
            ['year' => $currentYear, 'month' => 12],
            [
                'weekend_days' => [5, 6], // Friday and Saturday
                'description' => 'Holiday season weekend configuration',
                'is_active' => true,
            ]
        );

        // Example: Different weekend for a summer month (maybe Sunday-Monday)
        WeekendConfiguration::updateOrCreate(
            ['year' => $currentYear, 'month' => 7],
            [
                'weekend_days' => [0, 1], // Sunday and Monday
                'description' => 'Summer schedule weekend configuration',
                'is_active' => true,
            ]
        );

        $this->command->info('Weekend and Holiday configurations seeded successfully!');
    }
}
