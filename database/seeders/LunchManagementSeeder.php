<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\LunchPrice;
use App\Models\LunchOrder;
use App\Models\Bill;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class LunchManagementSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'mode' => 'regular',
            'status' => 'active',
        ]);

        // Create regular users
        $regularUsers = [
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
        ];

        foreach ($regularUsers as $userData) {
            User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'password' => Hash::make('password'),
                'role' => 'user',
                'mode' => 'regular',
                'status' => 'active',
            ]);
        }

        // Create guest users
        $guestUsers = [
            ['name' => 'Alice Johnson', 'email' => '<EMAIL>'],
            ['name' => 'Charlie Brown', 'email' => '<EMAIL>'],
        ];

        foreach ($guestUsers as $userData) {
            User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'password' => Hash::make('password'),
                'role' => 'user',
                'mode' => 'guest',
                'status' => 'active',
            ]);
        }

        // Set lunch prices for current and previous months
        $currentYear = Carbon::now()->year;
        $currentMonth = Carbon::now()->month;
        $previousMonth = $currentMonth > 1 ? $currentMonth - 1 : 12;
        $previousYear = $currentMonth > 1 ? $currentYear : $currentYear - 1;

        LunchPrice::create([
            'year' => $currentYear,
            'month' => $currentMonth,
            'price' => 120.00,
        ]);

        LunchPrice::create([
            'year' => $previousYear,
            'month' => $previousMonth,
            'price' => 100.00,
        ]);

        // Create some sample lunch orders for the current month
        $users = User::where('role', 'user')->get();
        $startOfMonth = Carbon::now()->startOfMonth();
        $today = Carbon::now();

        foreach ($users as $user) {
            // Create orders for the first half of the month
            for ($day = 1; $day <= min(15, $today->day); $day++) {
                $orderDate = $startOfMonth->copy()->addDays($day - 1);
                
                // Skip weekends
                if ($orderDate->isWeekend()) {
                    continue;
                }

                // Regular users get auto orders, some cancelled
                if ($user->isRegular()) {
                    $status = (rand(1, 10) > 8) ? 'cancelled' : 'ordered'; // 20% cancellation rate
                } else {
                    // Guest users order manually, less frequently
                    if (rand(1, 10) > 6) { // 40% order rate for guests
                        $status = 'ordered';
                    } else {
                        continue; // Skip this day
                    }
                }

                LunchOrder::create([
                    'user_id' => $user->id,
                    'order_date' => $orderDate,
                    'status' => $status,
                ]);
            }
        }

        // Generate bills for the previous month
        foreach ($users as $user) {
            Bill::generateForUser($user, $previousYear, $previousMonth);
        }

        $this->command->info('Lunch Management System seeded successfully!');
        $this->command->info('Admin credentials: <EMAIL> / password');
        $this->command->info('User credentials: <EMAIL> / password (and others)');
    }
}
