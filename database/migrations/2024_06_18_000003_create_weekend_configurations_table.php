<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('weekend_configurations', function (Blueprint $table) {
            $table->id();
            $table->integer('year');
            $table->integer('month');
            $table->json('weekend_days'); // Array of day numbers (0=Sunday, 1=Monday, etc.)
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Ensure one configuration per month
            $table->unique(['year', 'month']);
            
            // Add indexes for performance
            $table->index(['year', 'month', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('weekend_configurations');
    }
};
