<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('holiday_configurations', function (Blueprint $table) {
            $table->id();
            $table->integer('year');
            $table->integer('month');
            $table->date('holiday_date');
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['fixed', 'variable', 'custom'])->default('custom');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Prevent duplicate holidays on same date
            $table->unique(['holiday_date', 'year', 'month']);
            
            // Add indexes for performance
            $table->index(['year', 'month', 'is_active']);
            $table->index(['holiday_date', 'is_active']);
            $table->index(['type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('holiday_configurations');
    }
};
