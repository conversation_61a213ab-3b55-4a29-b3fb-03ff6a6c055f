<?php

namespace App\Console\Commands;

use App\Services\LunchOrderService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CreateAutoOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lunch:auto-order {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create automatic lunch orders for regular users';

    protected $lunchOrderService;

    public function __construct(LunchOrderService $lunchOrderService)
    {
        parent::__construct();
        $this->lunchOrderService = $lunchOrderService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->argument('date') ? Carbon::parse($this->argument('date')) : Carbon::today();
        
        $this->info("Creating auto orders for {$date->format('Y-m-d')}...");
        
        $ordersCreated = $this->lunchOrderService->createAutoOrdersForDate($date);
        
        $this->info("Created {$ordersCreated} automatic lunch orders.");
        
        return 0;
    }
}
