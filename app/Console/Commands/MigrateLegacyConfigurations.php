<?php

namespace App\Console\Commands;

use App\Services\BusinessDayService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class MigrateLegacyConfigurations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'config:migrate-legacy {--year=} {--all-years}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate legacy weekend and holiday configurations to database';

    protected $businessDayService;

    public function __construct(BusinessDayService $businessDayService)
    {
        parent::__construct();
        $this->businessDayService = $businessDayService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting legacy configuration migration...');

        if ($this->option('all-years')) {
            $years = range(2020, 2030);
        } else {
            $year = $this->option('year') ?? Carbon::now()->year;
            $years = [$year];
        }

        $totalMigrated = 0;
        $totalEnsured = 0;

        foreach ($years as $year) {
            $this->info("Processing year {$year}...");

            // Migrate legacy holidays
            $migratedHolidays = $this->businessDayService->migrateLegacyHolidays($year);
            $totalMigrated += $migratedHolidays;

            if ($migratedHolidays > 0) {
                $this->line("  Migrated {$migratedHolidays} legacy holidays");
            }

            // Ensure default weekend configurations exist
            $ensuredWeekends = 0;
            for ($month = 1; $month <= 12; $month++) {
                if ($this->businessDayService->ensureDefaultWeekendConfiguration($year, $month)) {
                    $ensuredWeekends++;
                }
            }

            $totalEnsured += $ensuredWeekends;

            if ($ensuredWeekends > 0) {
                $this->line("  Created {$ensuredWeekends} default weekend configurations");
            }
        }

        $this->info("Migration completed!");
        $this->info("Total legacy holidays migrated: {$totalMigrated}");
        $this->info("Total default weekend configurations created: {$totalEnsured}");

        // Show configuration status
        if (count($years) === 1) {
            $this->showConfigurationStatus($years[0]);
        }

        return 0;
    }

    /**
     * Show configuration status for a year.
     */
    private function showConfigurationStatus(int $year)
    {
        $this->info("\nConfiguration Status for {$year}:");
        $this->line(str_repeat('-', 50));

        $headers = ['Month', 'Weekend Config', 'Holidays', 'Business Days'];
        $rows = [];

        for ($month = 1; $month <= 12; $month++) {
            $status = $this->businessDayService->getConfigurationStatus($year, $month);
            
            $rows[] = [
                $status['month_name'],
                $status['weekend_configured'] ? '✓ Custom' : '✗ Default',
                $status['holiday_count'],
                $status['estimated_business_days']
            ];
        }

        $this->table($headers, $rows);
    }
}
