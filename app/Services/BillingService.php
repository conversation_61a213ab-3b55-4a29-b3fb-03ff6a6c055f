<?php

namespace App\Services;

use App\Models\User;
use App\Models\Bill;
use App\Models\LunchPrice;
use Carbon\Carbon;

class BillingService
{
    /**
     * Generate bills for all users for a specific month.
     */
    public function generateBillsForMonth($year = null, $month = null)
    {
        $year = $year ?? Carbon::now()->year;
        $month = $month ?? Carbon::now()->month;

        $users = User::where('role', 'user')->get();
        $billsGenerated = 0;

        foreach ($users as $user) {
            $bill = Bill::generateForUser($user, $year, $month);
            if ($bill->wasRecentlyCreated || $bill->wasChanged()) {
                $billsGenerated++;
            }
        }

        return $billsGenerated;
    }

    /**
     * Generate bill for a specific user for a specific month.
     */
    public function generateBillForUser(User $user, $year = null, $month = null)
    {
        $year = $year ?? Carbon::now()->year;
        $month = $month ?? Carbon::now()->month;

        return Bill::generateForUser($user, $year, $month);
    }

    /**
     * Get bill for a user for a specific month.
     */
    public function getBillForUser(User $user, $year = null, $month = null)
    {
        $year = $year ?? Carbon::now()->year;
        $month = $month ?? Carbon::now()->month;

        return Bill::where('user_id', $user->id)
                  ->where('year', $year)
                  ->where('month', $month)
                  ->first();
    }

    /**
     * Get all bills for a specific month.
     */
    public function getBillsForMonth($year = null, $month = null)
    {
        $year = $year ?? Carbon::now()->year;
        $month = $month ?? Carbon::now()->month;

        return Bill::with('user')
                  ->forMonth($year, $month)
                  ->get();
    }

    /**
     * Set lunch price for a specific month.
     */
    public function setLunchPrice($year, $month, $price)
    {
        return LunchPrice::setPriceForMonth($year, $month, $price);
    }

    /**
     * Get lunch price for a specific month.
     */
    public function getLunchPrice($year = null, $month = null)
    {
        $year = $year ?? Carbon::now()->year;
        $month = $month ?? Carbon::now()->month;

        return LunchPrice::getPriceForMonth($year, $month);
    }

    /**
     * Get all lunch prices.
     */
    public function getAllLunchPrices()
    {
        return LunchPrice::orderBy('year', 'desc')
                        ->orderBy('month', 'desc')
                        ->get();
    }

    /**
     * Calculate total amount for all bills in a month.
     */
    public function getTotalAmountForMonth($year = null, $month = null)
    {
        $year = $year ?? Carbon::now()->year;
        $month = $month ?? Carbon::now()->month;

        return Bill::forMonth($year, $month)->sum('amount');
    }

    /**
     * Get monthly summary with total lunches and amount.
     */
    public function getMonthlySummary($year = null, $month = null)
    {
        $year = $year ?? Carbon::now()->year;
        $month = $month ?? Carbon::now()->month;

        $bills = $this->getBillsForMonth($year, $month);
        
        return [
            'year' => $year,
            'month' => $month,
            'month_name' => Carbon::create($year, $month, 1)->format('F Y'),
            'total_users' => $bills->count(),
            'total_lunches' => $bills->sum('lunch_count'),
            'total_amount' => $bills->sum('amount'),
            'lunch_price' => $this->getLunchPrice($year, $month),
            'bills' => $bills,
        ];
    }
}
