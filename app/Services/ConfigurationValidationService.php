<?php

namespace App\Services;

use App\Models\WeekendConfiguration;
use App\Models\HolidayConfiguration;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ConfigurationValidationService
{
    /**
     * Validate weekend configuration for potential issues.
     */
    public function validateWeekendConfiguration(int $year, int $month, array $weekendDays): array
    {
        $issues = [];
        $warnings = [];

        // Check if all days are weekends
        if (count($weekendDays) >= 7) {
            $issues[] = 'Cannot set all days as weekends. At least one business day is required.';
        }

        // Check if no weekends are set
        if (empty($weekendDays)) {
            $warnings[] = 'No weekend days selected. This will result in all days being business days.';
        }

        // Check for unusual weekend patterns
        if (count($weekendDays) === 1) {
            $warnings[] = 'Only one weekend day selected. This is unusual and may affect employee satisfaction.';
        }

        if (count($weekendDays) >= 5) {
            $warnings[] = 'Five or more weekend days selected. This significantly reduces business days.';
        }

        // Check for non-consecutive weekend days that might be confusing
        if (count($weekendDays) === 2 && !$this->areConsecutive($weekendDays)) {
            $warnings[] = 'Non-consecutive weekend days selected. This may be confusing for employees.';
        }

        // Check impact on business days
        $daysInMonth = Carbon::create($year, $month, 1)->daysInMonth;
        $estimatedBusinessDays = $this->estimateBusinessDays($year, $month, $weekendDays);
        
        if ($estimatedBusinessDays < 15) {
            $warnings[] = "Only {$estimatedBusinessDays} estimated business days in this month. This may impact operations.";
        }

        // Check for conflicts with existing holidays
        $holidays = HolidayConfiguration::getForMonth($year, $month);
        $holidaysOnWeekends = $holidays->filter(function($holiday) use ($weekendDays) {
            return in_array($holiday->holiday_date->dayOfWeek, $weekendDays);
        });

        if ($holidaysOnWeekends->count() > 0) {
            $warnings[] = "{$holidaysOnWeekends->count()} existing holidays fall on the new weekend days.";
        }

        return [
            'valid' => empty($issues),
            'issues' => $issues,
            'warnings' => $warnings,
            'estimated_business_days' => $estimatedBusinessDays,
            'impact_analysis' => $this->analyzeWeekendImpact($year, $month, $weekendDays)
        ];
    }

    /**
     * Validate holiday configuration for potential issues.
     */
    public function validateHolidayConfiguration(string $date, string $name, string $type): array
    {
        $issues = [];
        $warnings = [];
        $carbonDate = Carbon::parse($date);

        // Check for duplicate holidays
        $existingHoliday = HolidayConfiguration::where('holiday_date', $carbonDate->format('Y-m-d'))
            ->where('is_active', true)
            ->first();

        if ($existingHoliday) {
            $issues[] = "A holiday already exists on {$carbonDate->format('F j, Y')}: {$existingHoliday->name}";
        }

        // Check if holiday falls on weekend
        $weekendDays = WeekendConfiguration::getWeekendDaysForMonth($carbonDate->year, $carbonDate->month);
        if (in_array($carbonDate->dayOfWeek, $weekendDays)) {
            $warnings[] = "Holiday falls on a weekend day ({$carbonDate->format('l')}). Consider if this is necessary.";
        }

        // Check holiday density in the month
        $monthHolidays = HolidayConfiguration::getForMonth($carbonDate->year, $carbonDate->month);
        if ($monthHolidays->count() >= 5) {
            $warnings[] = "This month already has {$monthHolidays->count()} holidays. Consider if another is necessary.";
        }

        // Check for holidays too close together
        $nearbyHolidays = HolidayConfiguration::where('holiday_date', '>=', $carbonDate->copy()->subDays(3)->format('Y-m-d'))
            ->where('holiday_date', '<=', $carbonDate->copy()->addDays(3)->format('Y-m-d'))
            ->where('is_active', true)
            ->get();

        if ($nearbyHolidays->count() > 0) {
            $warnings[] = "There are {$nearbyHolidays->count()} other holidays within 3 days. This creates an extended break.";
        }

        // Validate fixed holiday dates
        if ($type === 'fixed') {
            $expectedDate = $this->getExpectedFixedHolidayDate($name);
            if ($expectedDate && $expectedDate !== $carbonDate->format('m-d')) {
                $warnings[] = "{$name} is typically celebrated on {$expectedDate}, but you've set it for {$carbonDate->format('m-d')}.";
            }
        }

        return [
            'valid' => empty($issues),
            'issues' => $issues,
            'warnings' => $warnings,
            'impact_analysis' => $this->analyzeHolidayImpact($carbonDate)
        ];
    }

    /**
     * Analyze the impact of weekend configuration changes.
     */
    public function analyzeWeekendImpact(int $year, int $month, array $weekendDays): array
    {
        $daysInMonth = Carbon::create($year, $month, 1)->daysInMonth;
        $businessDays = $this->estimateBusinessDays($year, $month, $weekendDays);
        $weekendDaysCount = $this->countWeekendDaysInMonth($year, $month, $weekendDays);
        
        // Get current configuration for comparison
        $currentConfig = WeekendConfiguration::getForMonth($year, $month);
        $currentWeekendDays = $currentConfig ? $currentConfig->weekend_days : [0, 6];
        $currentBusinessDays = $this->estimateBusinessDays($year, $month, $currentWeekendDays);
        
        return [
            'total_days' => $daysInMonth,
            'business_days' => $businessDays,
            'weekend_days' => $weekendDaysCount,
            'business_day_percentage' => round(($businessDays / $daysInMonth) * 100, 1),
            'change_from_current' => $businessDays - $currentBusinessDays,
            'productivity_impact' => $this->calculateProductivityImpact($businessDays, $currentBusinessDays),
            'employee_satisfaction_impact' => $this->calculateSatisfactionImpact($weekendDays, $currentWeekendDays)
        ];
    }

    /**
     * Analyze the impact of adding a holiday.
     */
    public function analyzeHolidayImpact(Carbon $date): array
    {
        $month = $date->month;
        $year = $date->year;
        
        $businessDays = $this->estimateBusinessDays($year, $month, WeekendConfiguration::getWeekendDaysForMonth($year, $month));
        $currentHolidays = HolidayConfiguration::getForMonth($year, $month)->count();
        
        return [
            'reduces_business_days_by' => 1,
            'new_business_days_count' => $businessDays - 1,
            'total_holidays_in_month' => $currentHolidays + 1,
            'falls_on' => $date->format('l'),
            'creates_long_weekend' => $this->createsLongWeekend($date),
            'productivity_impact' => 'Low to Medium'
        ];
    }

    /**
     * Check if weekend days are consecutive.
     */
    private function areConsecutive(array $days): bool
    {
        if (count($days) < 2) return true;
        
        sort($days);
        
        // Handle wrap-around (Saturday=6, Sunday=0)
        if (in_array(0, $days) && in_array(6, $days)) {
            // Remove 0 and 6, check if remaining are consecutive
            $filtered = array_filter($days, fn($day) => $day !== 0 && $day !== 6);
            if (empty($filtered)) return true;
            
            sort($filtered);
            for ($i = 1; $i < count($filtered); $i++) {
                if ($filtered[$i] - $filtered[$i-1] !== 1) return false;
            }
            return true;
        }
        
        // Normal consecutive check
        for ($i = 1; $i < count($days); $i++) {
            if ($days[$i] - $days[$i-1] !== 1) return false;
        }
        
        return true;
    }

    /**
     * Estimate business days in a month given weekend configuration.
     */
    private function estimateBusinessDays(int $year, int $month, array $weekendDays): int
    {
        $startDate = Carbon::create($year, $month, 1);
        $endDate = $startDate->copy()->endOfMonth();
        $businessDays = 0;
        
        $current = $startDate->copy();
        while ($current->lte($endDate)) {
            if (!in_array($current->dayOfWeek, $weekendDays)) {
                $businessDays++;
            }
            $current->addDay();
        }
        
        // Subtract holidays
        $holidays = HolidayConfiguration::getForMonth($year, $month)->count();
        
        return max(0, $businessDays - $holidays);
    }

    /**
     * Count weekend days in a month.
     */
    private function countWeekendDaysInMonth(int $year, int $month, array $weekendDays): int
    {
        $startDate = Carbon::create($year, $month, 1);
        $endDate = $startDate->copy()->endOfMonth();
        $weekendCount = 0;
        
        $current = $startDate->copy();
        while ($current->lte($endDate)) {
            if (in_array($current->dayOfWeek, $weekendDays)) {
                $weekendCount++;
            }
            $current->addDay();
        }
        
        return $weekendCount;
    }

    /**
     * Get expected date for common fixed holidays.
     */
    private function getExpectedFixedHolidayDate(string $name): ?string
    {
        $fixedHolidays = [
            'new year' => '01-01',
            'christmas' => '12-25',
            'independence day' => '07-04',
            'valentine' => '02-14',
            'halloween' => '10-31',
            'labor day' => '05-01',
        ];
        
        $name = strtolower($name);
        foreach ($fixedHolidays as $holiday => $date) {
            if (stripos($name, $holiday) !== false) {
                return $date;
            }
        }
        
        return null;
    }

    /**
     * Calculate productivity impact of weekend changes.
     */
    private function calculateProductivityImpact(int $newBusinessDays, int $currentBusinessDays): string
    {
        $change = $newBusinessDays - $currentBusinessDays;
        
        if ($change > 2) return 'High Positive';
        if ($change > 0) return 'Moderate Positive';
        if ($change === 0) return 'No Change';
        if ($change > -3) return 'Moderate Negative';
        return 'High Negative';
    }

    /**
     * Calculate employee satisfaction impact.
     */
    private function calculateSatisfactionImpact(array $newWeekendDays, array $currentWeekendDays): string
    {
        $newWeekendCount = count($newWeekendDays);
        $currentWeekendCount = count($currentWeekendDays);
        
        if ($newWeekendCount > $currentWeekendCount) return 'Positive';
        if ($newWeekendCount < $currentWeekendCount) return 'Negative';
        return 'Neutral';
    }

    /**
     * Check if a holiday creates a long weekend.
     */
    private function createsLongWeekend(Carbon $date): bool
    {
        $weekendDays = WeekendConfiguration::getWeekendDaysForMonth($date->year, $date->month);
        
        // Check if the day before or after is a weekend
        $dayBefore = $date->copy()->subDay();
        $dayAfter = $date->copy()->addDay();
        
        return in_array($dayBefore->dayOfWeek, $weekendDays) || 
               in_array($dayAfter->dayOfWeek, $weekendDays);
    }
}
