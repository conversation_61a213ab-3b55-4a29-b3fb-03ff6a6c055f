<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserService
{
    /**
     * Create a new user.
     */
    public function createUser($name, $email, $password, $role = 'user', $mode = 'regular', $status = 'active')
    {
        return User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'role' => $role,
            'mode' => $mode,
            'status' => $status,
        ]);
    }

    /**
     * Update user role.
     */
    public function updateUserRole(User $user, $role)
    {
        $user->update(['role' => $role]);
        return $user;
    }

    /**
     * Update user mode.
     */
    public function updateUserMode(User $user, $mode)
    {
        $user->update(['mode' => $mode]);
        return $user;
    }

    /**
     * Update user status.
     */
    public function updateUserStatus(User $user, $status)
    {
        $user->update(['status' => $status]);
        return $user;
    }

    /**
     * Get all users with their lunch statistics.
     */
    public function getAllUsersWithStats()
    {
        return User::where('role', 'user')
                  ->withCount(['lunchOrders', 'bills'])
                  ->get();
    }

    /**
     * Get regular users.
     */
    public function getRegularUsers()
    {
        return User::where('role', 'user')
                  ->where('mode', 'regular')
                  ->get();
    }

    /**
     * Get guest users.
     */
    public function getGuestUsers()
    {
        return User::where('role', 'user')
                  ->where('mode', 'guest')
                  ->get();
    }

    /**
     * Get admin users.
     */
    public function getAdminUsers()
    {
        return User::where('role', 'admin')->get();
    }

    /**
     * Delete a user (only if not admin).
     */
    public function deleteUser(User $user)
    {
        if ($user->isAdmin()) {
            throw new \Exception('Cannot delete admin user');
        }

        return $user->delete();
    }

    /**
     * Toggle user mode between regular and guest.
     */
    public function toggleUserMode(User $user)
    {
        $newMode = $user->isRegular() ? 'guest' : 'regular';
        return $this->updateUserMode($user, $newMode);
    }
}
