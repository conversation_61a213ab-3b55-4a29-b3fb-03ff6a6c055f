<?php

namespace App\Services;

use App\Models\User;
use App\Models\LunchOrder;
use App\Services\BusinessDayService;
use Carbon\Carbon;

class LunchOrderService
{
    protected $businessDayService;

    public function __construct(BusinessDayService $businessDayService)
    {
        $this->businessDayService = $businessDayService;
    }
    /**
     * Create auto orders for all regular users for a specific date.
     */
    public function createAutoOrdersForDate($date = null)
    {
        $date = $date ? Carbon::parse($date) : Carbon::today();

        // Skip if not a business day
        if (!$this->businessDayService->isBusinessDay($date)) {
            return 0;
        }

        $regularUsers = User::where('mode', 'regular')
                           ->where('status', 'active')
                           ->get();
        $ordersCreated = 0;

        foreach ($regularUsers as $user) {
            // Check if order already exists for this date
            $existingOrder = LunchOrder::where('user_id', $user->id)
                                     ->where('order_date', $date)
                                     ->first();

            if (!$existingOrder) {
                LunchOrder::create([
                    'user_id' => $user->id,
                    'order_date' => $date,
                    'status' => 'ordered',
                ]);
                $ordersCreated++;
            }
        }

        return $ordersCreated;
    }

    /**
     * Cancel lunch order for a user on a specific date.
     */
    public function cancelOrder(User $user, $date = null)
    {
        $date = $date ? Carbon::parse($date) : Carbon::today();

        // Check cancellation rules based on date
        if ($date->isToday() && !$this->isWithinOrderingTime()) {
            throw new \Exception('Cannot cancel lunch order after 10:00 AM.');
        }

        if ($date->isTomorrow() && !$this->isAfter2PM()) {
            throw new \Exception('Cannot cancel tomorrow\'s lunch order before 2:00 PM today.');
        }

        $order = LunchOrder::where('user_id', $user->id)
                          ->where('order_date', $date)
                          ->first();

        if ($order) {
            $order->cancel();
            return true;
        }

        return false;
    }

    /**
     * Place lunch order for a guest user on a specific date.
     */
    public function placeOrder(User $user, $date = null)
    {
        $date = $date ? Carbon::parse($date) : Carbon::today();

        // Check if user is active
        if (!$user->isActive()) {
            throw new \Exception('Cannot place order for inactive user.');
        }

        // Check ordering time restrictions
        if ($date->isToday() && !$this->isWithinOrderingTime()) {
            throw new \Exception('Cannot place lunch order after 10:00 AM.');
        }

        // For tomorrow's orders, guest users can order anytime, regular users cannot
        if ($date->isTomorrow() && $user->mode === 'regular') {
            throw new \Exception('Regular users cannot manually place orders for tomorrow. Orders are automatically created.');
        }

        // Check if it's a business day
        if (!$this->businessDayService->isBusinessDay($date)) {
            if ($this->businessDayService->isWeekend($date)) {
                throw new \Exception('Cannot place lunch order on weekends.');
            } elseif ($this->businessDayService->isHoliday($date)) {
                throw new \Exception('Cannot place lunch order on holidays.');
            } else {
                throw new \Exception('Cannot place lunch order on non-business days.');
            }
        }

        // Create or update the order
        $order = LunchOrder::updateOrCreate(
            [
                'user_id' => $user->id,
                'order_date' => $date,
            ],
            [
                'status' => 'ordered',
            ]
        );

        return $order;
    }

    /**
     * Get lunch orders for a specific date.
     */
    public function getOrdersForDate($date = null)
    {
        $date = $date ? Carbon::parse($date) : Carbon::today();

        return LunchOrder::with('user')
                        ->forDate($date)
                        ->ordered()
                        ->get();
    }

    /**
     * Get cancelled orders for a specific date.
     */
    public function getCancelledOrdersForDate($date = null)
    {
        $date = $date ? Carbon::parse($date) : Carbon::today();

        return LunchOrder::with('user')
                        ->forDate($date)
                        ->cancelled()
                        ->get();
    }

    /**
     * Get user's order for a specific date.
     */
    public function getUserOrderForDate(User $user, $date = null)
    {
        $date = $date ? Carbon::parse($date) : Carbon::today();

        return LunchOrder::where('user_id', $user->id)
                        ->where('order_date', $date)
                        ->first();
    }

    /**
     * Check if user has ordered lunch for a specific date.
     */
    public function hasUserOrderedForDate(User $user, $date = null)
    {
        $order = $this->getUserOrderForDate($user, $date);
        return $order && $order->isOrdered();
    }

    /**
     * Create auto orders for all regular users for all business days in a month.
     */
    public function createAutoOrdersForMonth(int $year, int $month)
    {
        $businessDays = $this->businessDayService->getBusinessDaysForMonth($year, $month);
        $totalOrdersCreated = 0;

        foreach ($businessDays as $date) {
            $ordersCreated = $this->createAutoOrdersForDate($date);
            $totalOrdersCreated += $ordersCreated;
        }

        return $totalOrdersCreated;
    }

    /**
     * Get month summary with business day information.
     */
    public function getMonthSummaryWithBusinessDays(int $year, int $month)
    {
        $monthSummary = $this->businessDayService->getMonthSummary($year, $month);

        // Get orders for the month
        $orders = LunchOrder::with('user')
                           ->forMonth($year, $month)
                           ->get();

        $orderedCount = $orders->where('status', 'ordered')->count();
        $cancelledCount = $orders->where('status', 'cancelled')->count();

        return array_merge($monthSummary, [
            'total_orders' => $orders->count(),
            'ordered_count' => $orderedCount,
            'cancelled_count' => $cancelledCount,
            'orders' => $orders,
        ]);
    }

    /**
     * Get orders for business days only in a month.
     */
    public function getBusinessDayOrdersForMonth(int $year, int $month)
    {
        $businessDays = $this->businessDayService->getBusinessDaysForMonth($year, $month);
        $businessDayDates = $businessDays->map(function($date) {
            return $date->format('Y-m-d');
        })->toArray();

        return LunchOrder::with('user')
                        ->forMonth($year, $month)
                        ->whereIn('order_date', $businessDayDates)
                        ->get();
    }

    /**
     * Check if today is a valid day for lunch ordering.
     */
    public function canOrderToday(): array
    {
        $today = Carbon::today();

        // Check if it's a business day first
        if (!$this->businessDayService->isBusinessDay($today)) {
            $nextBusinessDay = $this->businessDayService->getNextBusinessDay($today);

            return [
                'can_order' => false,
                'message' => $this->getCannotOrderMessage($today),
                'date' => $today,
                'next_business_day' => $nextBusinessDay,
                'is_business_day' => false,
                'is_weekend' => $this->businessDayService->isWeekend($today),
                'is_holiday' => $this->businessDayService->isHoliday($today),
                'is_past_deadline' => false,
            ];
        }

        // Check if it's past the ordering deadline (10 AM)
        if (!$this->isWithinOrderingTime()) {
            return [
                'can_order' => false,
                'message' => 'Lunch ordering deadline has passed. Orders must be placed before 10:00 AM.',
                'date' => $today,
                'is_business_day' => true,
                'is_weekend' => false,
                'is_holiday' => false,
                'is_past_deadline' => true,
                'deadline_time' => '10:00 AM',
                'current_time' => Carbon::now()->format('g:i A'),
            ];
        }

        return [
            'can_order' => true,
            'message' => 'You can place lunch orders today.',
            'date' => $today,
            'is_business_day' => true,
            'is_weekend' => false,
            'is_holiday' => false,
            'is_past_deadline' => false,
            'deadline_time' => '10:00 AM',
            'current_time' => Carbon::now()->format('g:i A'),
        ];
    }

    /**
     * Get appropriate message for why ordering is not allowed.
     */
    private function getCannotOrderMessage(Carbon $date): string
    {
        if ($this->businessDayService->isWeekend($date)) {
            return 'Lunch ordering is not available on weekends.';
        } elseif ($this->businessDayService->isHoliday($date)) {
            return 'Lunch ordering is not available on holidays.';
        } else {
            return 'Lunch ordering is not available on non-business days.';
        }
    }

    /**
     * Check if current time is within the ordering deadline (before 10 AM).
     */
    private function isWithinOrderingTime(): bool
    {
        $now = Carbon::now();
        $deadline = Carbon::today()->setTime(10, 0, 0); // 10:00 AM

        return $now->lt($deadline);
    }

    /**
     * Check if current time is after 2 PM (for next day cancellations).
     */
    private function isAfter2PM(): bool
    {
        $now = Carbon::now();
        $cutoffTime = Carbon::today()->setTime(14, 0, 0); // 2:00 PM

        return $now->gte($cutoffTime);
    }

    /**
     * Check if user can place an order for tomorrow.
     */
    public function canOrderTomorrow(User $user): array
    {
        $tomorrow = Carbon::tomorrow();

        // Check if tomorrow is a business day
        if (!$this->businessDayService->isBusinessDay($tomorrow)) {
            return [
                'can_order' => false,
                'message' => 'Tomorrow is not a business day.',
                'date' => $tomorrow,
                'is_business_day' => false
            ];
        }

        // Regular users cannot manually place tomorrow's orders
        if ($user->mode === 'regular') {
            return [
                'can_order' => false,
                'message' => 'Regular users cannot manually place orders for tomorrow. Orders are automatically created.',
                'date' => $tomorrow,
                'is_business_day' => true
            ];
        }

        // Guest users can always place tomorrow's orders
        return [
            'can_order' => true,
            'message' => 'You can place lunch orders for tomorrow.',
            'date' => $tomorrow,
            'is_business_day' => true
        ];
    }

    /**
     * Get the ordering deadline time.
     */
    public function getOrderingDeadline(): Carbon
    {
        return Carbon::today()->setTime(10, 0, 0); // 10:00 AM
    }

    /**
     * Get time remaining until ordering deadline.
     */
    public function getTimeUntilDeadline(): ?string
    {
        if (!$this->isWithinOrderingTime()) {
            return null; // Past deadline
        }

        $now = Carbon::now();
        $deadline = $this->getOrderingDeadline();
        $diff = $now->diff($deadline);

        if ($diff->h > 0) {
            return $diff->h . ' hour(s) and ' . $diff->i . ' minute(s)';
        } else {
            return $diff->i . ' minute(s)';
        }
    }

    /**
     * Get user's upcoming lunch orders (future dates).
     */
    public function getUpcomingOrders(User $user, int $limit = 30): \Illuminate\Database\Eloquent\Collection
    {
        $tomorrow = Carbon::tomorrow();
        $endDate = Carbon::now()->addDays($limit);

        return LunchOrder::where('user_id', $user->id)
                        ->where('order_date', '>=', $tomorrow)
                        ->where('order_date', '<=', $endDate)
                        ->orderBy('order_date', 'asc')
                        ->get();
    }

    /**
     * Get user's upcoming business day orders only.
     */
    public function getUpcomingBusinessDayOrders(User $user, int $limit = 30): \Illuminate\Database\Eloquent\Collection
    {
        $upcomingOrders = $this->getUpcomingOrders($user, $limit);

        return $upcomingOrders->filter(function ($order) {
            return $this->businessDayService->isBusinessDay($order->order_date);
        });
    }

    /**
     * Check if an order can be cancelled based on date and time rules.
     */
    public function canCancelOrder(User $user, $date): array
    {
        $date = $date ? Carbon::parse($date) : Carbon::today();

        // Check if order exists
        $order = $this->getUserOrderForDate($user, $date);
        if (!$order) {
            return [
                'can_cancel' => false,
                'reason' => 'No order found for this date.',
                'order' => null
            ];
        }

        // Check if order is already cancelled
        if ($order->isCancelled()) {
            return [
                'can_cancel' => false,
                'reason' => 'Order is already cancelled.',
                'order' => $order
            ];
        }

        // Tomorrow's orders can be cancelled after 2 PM today
        if ($date->isTomorrow()) {
            if ($this->isAfter2PM()) {
                return [
                    'can_cancel' => true,
                    'reason' => 'Tomorrow\'s order can be cancelled after 2:00 PM today.',
                    'order' => $order
                ];
            } else {
                return [
                    'can_cancel' => false,
                    'reason' => 'Tomorrow\'s order can only be cancelled after 2:00 PM today.',
                    'order' => $order
                ];
            }
        }

        // Future orders (beyond tomorrow) can always be cancelled
        if ($date->isFuture() && !$date->isTomorrow()) {
            return [
                'can_cancel' => true,
                'reason' => 'Future orders can be cancelled anytime.',
                'order' => $order
            ];
        }

        // Today's orders can only be cancelled before 10 AM
        if ($date->isToday()) {
            if ($this->isWithinOrderingTime()) {
                return [
                    'can_cancel' => true,
                    'reason' => 'Today\'s order can be cancelled before 10:00 AM.',
                    'order' => $order
                ];
            } else {
                return [
                    'can_cancel' => false,
                    'reason' => 'Cannot cancel today\'s order after 10:00 AM.',
                    'order' => $order
                ];
            }
        }

        // Past orders cannot be cancelled
        return [
            'can_cancel' => false,
            'reason' => 'Cannot cancel past orders.',
            'order' => $order
        ];
    }

    /**
     * Get count of upcoming orders that can be cancelled.
     */
    public function getCancellableUpcomingOrdersCount(User $user): int
    {
        $upcomingOrders = $this->getUpcomingOrders($user);

        return $upcomingOrders->filter(function ($order) use ($user) {
            $cancelStatus = $this->canCancelOrder($user, $order->order_date);
            return $cancelStatus['can_cancel'] && $order->isOrdered();
        })->count();
    }

    /**
     * Get summary of user's upcoming orders.
     */
    public function getUpcomingOrdersSummary(User $user): array
    {
        $upcomingOrders = $this->getUpcomingOrders($user);

        $summary = [
            'total' => $upcomingOrders->count(),
            'ordered' => $upcomingOrders->where('status', 'ordered')->count(),
            'cancelled' => $upcomingOrders->where('status', 'cancelled')->count(),
            'cancellable' => 0,
            'business_days' => 0,
            'next_order_date' => null
        ];

        foreach ($upcomingOrders as $order) {
            // Check if it's a business day
            if ($this->businessDayService->isBusinessDay($order->order_date)) {
                $summary['business_days']++;
            }

            // Check if it can be cancelled
            if ($order->isOrdered()) {
                $cancelStatus = $this->canCancelOrder($user, $order->order_date);
                if ($cancelStatus['can_cancel']) {
                    $summary['cancellable']++;
                }
            }

            // Set next order date (first ordered future order)
            if (!$summary['next_order_date'] && $order->isOrdered()) {
                $summary['next_order_date'] = $order->order_date;
            }
        }

        return $summary;
    }
}
