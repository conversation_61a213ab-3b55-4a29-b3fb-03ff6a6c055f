<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use App\Models\WeekendConfiguration;
use App\Models\HolidayConfiguration;

class BusinessDayService
{
    /**
     * Default weekend days (0 = Sunday, 6 = Saturday)
     * Used as fallback when no configuration exists
     */
    protected array $defaultWeekendDays = [0, 6]; // Sunday and Saturday

    /**
     * Legacy fixed holidays (month-day format)
     * Kept for backward compatibility - new holidays should use HolidayConfiguration
     */
    protected array $legacyFixedHolidays = [
        '01-01', // New Year's Day
        '07-04', // Independence Day
        '12-25', // Christmas Day
    ];

    /**
     * Check if a given date is a weekend.
     */
    public function isWeekend(Carbon $date): bool
    {
        $weekendDays = WeekendConfiguration::getWeekendDaysForMonth($date->year, $date->month);
        return in_array($date->dayOfWeek, $weekendDays);
    }

    /**
     * Check if a given date is a business day (not weekend and not holiday).
     */
    public function isBusinessDay(Carbon $date): bool
    {
        return !$this->isWeekend($date) && !$this->isHoliday($date);
    }

    /**
     * Check if a given date is a holiday.
     */
    public function isHoliday(Carbon $date): bool
    {
        // Check database-configured holidays first
        if (HolidayConfiguration::isHoliday($date)) {
            return true;
        }

        // Fallback to legacy fixed holidays for backward compatibility
        $monthDay = $date->format('m-d');
        return in_array($monthDay, $this->legacyFixedHolidays);
    }

    /**
     * Get all business days for a given month and year.
     */
    public function getBusinessDaysForMonth(int $year, int $month): Collection
    {
        $startOfMonth = Carbon::create($year, $month, 1);
        $endOfMonth = $startOfMonth->copy()->endOfMonth();
        
        $businessDays = collect();
        $currentDate = $startOfMonth->copy();

        while ($currentDate->lte($endOfMonth)) {
            if ($this->isBusinessDay($currentDate)) {
                $businessDays->push($currentDate->copy());
            }
            $currentDate->addDay();
        }

        return $businessDays;
    }

    /**
     * Get all weekend days for a given month and year.
     */
    public function getWeekendsForMonth(int $year, int $month): Collection
    {
        $startOfMonth = Carbon::create($year, $month, 1);
        $endOfMonth = $startOfMonth->copy()->endOfMonth();
        $weekendDays = WeekendConfiguration::getWeekendDaysForMonth($year, $month);

        $weekends = collect();
        $currentDate = $startOfMonth->copy();

        while ($currentDate->lte($endOfMonth)) {
            if (in_array($currentDate->dayOfWeek, $weekendDays)) {
                $weekends->push($currentDate->copy());
            }
            $currentDate->addDay();
        }

        return $weekends;
    }

    /**
     * Get all holidays for a given month and year.
     */
    public function getHolidaysForMonth(int $year, int $month): Collection
    {
        $holidays = HolidayConfiguration::getForMonth($year, $month);

        return $holidays->map(function($holiday) {
            return $holiday->holiday_date;
        });
    }

    /**
     * Get the next business day from a given date.
     */
    public function getNextBusinessDay(Carbon $date): Carbon
    {
        $nextDay = $date->copy()->addDay();
        
        while (!$this->isBusinessDay($nextDay)) {
            $nextDay->addDay();
        }

        return $nextDay;
    }

    /**
     * Get the previous business day from a given date.
     */
    public function getPreviousBusinessDay(Carbon $date): Carbon
    {
        $previousDay = $date->copy()->subDay();
        
        while (!$this->isBusinessDay($previousDay)) {
            $previousDay->subDay();
        }

        return $previousDay;
    }

    /**
     * Count business days between two dates (inclusive).
     */
    public function countBusinessDaysBetween(Carbon $startDate, Carbon $endDate): int
    {
        $count = 0;
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            if ($this->isBusinessDay($currentDate)) {
                $count++;
            }
            $currentDate->addDay();
        }

        return $count;
    }

    /**
     * Get month summary with business days, weekends, and holidays.
     */
    public function getMonthSummary(int $year, int $month): array
    {
        $businessDays = $this->getBusinessDaysForMonth($year, $month);
        $weekends = $this->getWeekendsForMonth($year, $month);
        $holidays = $this->getHolidaysForMonth($year, $month);
        $holidayConfigs = $this->getHolidayConfiguration($year, $month);

        return [
            'year' => $year,
            'month' => $month,
            'month_name' => Carbon::create($year, $month, 1)->format('F Y'),
            'total_days' => Carbon::create($year, $month, 1)->daysInMonth,
            'business_days' => $businessDays,
            'business_days_count' => $businessDays->count(),
            'weekends' => $weekends,
            'weekends_count' => $weekends->count(),
            'holidays' => $holidays,
            'holidays_count' => $holidays->count(),
            'holiday_configs' => $holidayConfigs,
        ];
    }

    /**
     * Set custom weekend days (deprecated - use setWeekendConfiguration instead).
     * @deprecated Use setWeekendConfiguration for database-driven configuration
     */
    public function setWeekendDays(array $weekendDays): void
    {
        $this->defaultWeekendDays = $weekendDays;
    }

    /**
     * Add a fixed holiday (deprecated - use addHoliday instead).
     * @deprecated Use addHoliday for database-driven configuration
     */
    public function addFixedHoliday(string $monthDay): void
    {
        if (!in_array($monthDay, $this->legacyFixedHolidays)) {
            $this->legacyFixedHolidays[] = $monthDay;
        }
    }

    /**
     * Remove a fixed holiday (deprecated - use removeHoliday instead).
     * @deprecated Use removeHoliday for database-driven configuration
     */
    public function removeFixedHoliday(string $monthDay): void
    {
        $this->legacyFixedHolidays = array_filter($this->legacyFixedHolidays, function($holiday) use ($monthDay) {
            return $holiday !== $monthDay;
        });
    }

    /**
     * Get current weekend days (deprecated - use getWeekendConfiguration instead).
     * @deprecated Use getWeekendConfiguration for database-driven configuration
     */
    public function getWeekendDays(): array
    {
        return $this->defaultWeekendDays;
    }

    /**
     * Get current fixed holidays (deprecated - use getHolidayConfiguration instead).
     * @deprecated Use getHolidayConfiguration for database-driven configuration
     */
    public function getFixedHolidays(): array
    {
        return $this->legacyFixedHolidays;
    }

    /**
     * Set weekend configuration for a specific month.
     */
    public function setWeekendConfiguration(int $year, int $month, array $weekendDays, string $description = null)
    {
        return WeekendConfiguration::setForMonth($year, $month, $weekendDays, $description);
    }

    /**
     * Get weekend configuration for a specific month.
     */
    public function getWeekendConfiguration(int $year, int $month)
    {
        return WeekendConfiguration::getForMonth($year, $month);
    }

    /**
     * Add holiday for a specific date.
     */
    public function addHoliday(string $date, string $name, string $description = null, string $type = 'custom')
    {
        return HolidayConfiguration::addHoliday($date, $name, $description, $type);
    }

    /**
     * Get holiday configuration for a specific month.
     */
    public function getHolidayConfiguration(int $year, int $month)
    {
        return HolidayConfiguration::getForMonth($year, $month);
    }

    /**
     * Remove holiday by ID.
     */
    public function removeHoliday(int $holidayId)
    {
        $holiday = HolidayConfiguration::find($holidayId);
        if ($holiday) {
            return $holiday->delete();
        }
        return false;
    }

    /**
     * Get weekend configuration for all months in a year.
     */
    public function getWeekendConfigurationsForYear(int $year)
    {
        return WeekendConfiguration::getForYear($year);
    }

    /**
     * Get holiday configuration for all months in a year.
     */
    public function getHolidayConfigurationsForYear(int $year)
    {
        return HolidayConfiguration::getForYear($year);
    }

    /**
     * Migrate legacy fixed holidays to database configuration.
     */
    public function migrateLegacyHolidays(int $year): int
    {
        $migratedCount = 0;

        foreach ($this->legacyFixedHolidays as $monthDay) {
            $date = Carbon::createFromFormat('Y-m-d', "{$year}-{$monthDay}");

            // Check if holiday already exists
            $exists = HolidayConfiguration::where('holiday_date', $date->format('Y-m-d'))
                ->where('is_active', true)
                ->exists();

            if (!$exists) {
                $holidayNames = [
                    '01-01' => 'New Year\'s Day',
                    '07-04' => 'Independence Day',
                    '12-25' => 'Christmas Day',
                ];

                $name = $holidayNames[$monthDay] ?? 'Legacy Holiday';

                $this->addHoliday(
                    $date->format('Y-m-d'),
                    $name,
                    'Migrated from legacy configuration',
                    'fixed'
                );

                $migratedCount++;
            }
        }

        return $migratedCount;
    }

    /**
     * Ensure default weekend configuration exists for a month.
     */
    public function ensureDefaultWeekendConfiguration(int $year, int $month): bool
    {
        $existing = WeekendConfiguration::getForMonth($year, $month);

        if (!$existing) {
            $this->setWeekendConfiguration(
                $year,
                $month,
                $this->defaultWeekendDays,
                'Default weekend configuration'
            );
            return true;
        }

        return false;
    }

    /**
     * Get configuration status for a month.
     */
    public function getConfigurationStatus(int $year, int $month): array
    {
        $weekendConfig = $this->getWeekendConfiguration($year, $month);
        $holidayConfigs = $this->getHolidayConfiguration($year, $month);

        return [
            'month' => $month,
            'year' => $year,
            'month_name' => Carbon::create($year, $month, 1)->format('F Y'),
            'weekend_configured' => $weekendConfig !== null,
            'weekend_config' => $weekendConfig,
            'holiday_count' => $holidayConfigs->count(),
            'holidays' => $holidayConfigs,
            'using_defaults' => $weekendConfig === null,
            'estimated_business_days' => $this->estimateBusinessDays($year, $month),
            'configuration_complete' => $weekendConfig !== null && $holidayConfigs->count() > 0,
        ];
    }

    /**
     * Estimate business days for a month using current configuration.
     */
    private function estimateBusinessDays(int $year, int $month): int
    {
        $weekendDays = WeekendConfiguration::getWeekendDaysForMonth($year, $month);
        $startDate = Carbon::create($year, $month, 1);
        $endDate = $startDate->copy()->endOfMonth();
        $businessDays = 0;

        $current = $startDate->copy();
        while ($current->lte($endDate)) {
            if (!in_array($current->dayOfWeek, $weekendDays) && !$this->isHoliday($current)) {
                $businessDays++;
            }
            $current->addDay();
        }

        return $businessDays;
    }
}
