<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class WeekendConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'year',
        'month',
        'weekend_days',
        'description',
        'is_active',
    ];

    protected $casts = [
        'weekend_days' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get active configurations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get configuration for a specific month.
     */
    public function scopeForMonth($query, $year, $month)
    {
        return $query->where('year', $year)->where('month', $month);
    }

    /**
     * Get weekend configuration for a specific month.
     */
    public static function getForMonth($year, $month)
    {
        return self::active()
                  ->forMonth($year, $month)
                  ->first();
    }

    /**
     * Get weekend days for a specific month or return default.
     */
    public static function getWeekendDaysForMonth($year, $month)
    {
        $config = self::getForMonth($year, $month);
        
        if ($config) {
            return $config->weekend_days;
        }
        
        // Default weekend days (Saturday and Sunday)
        return [0, 6]; // 0 = Sunday, 6 = Saturday
    }

    /**
     * Set weekend configuration for a specific month.
     */
    public static function setForMonth($year, $month, $weekendDays, $description = null)
    {
        return self::updateOrCreate(
            ['year' => $year, 'month' => $month],
            [
                'weekend_days' => $weekendDays,
                'description' => $description,
                'is_active' => true,
            ]
        );
    }

    /**
     * Get formatted month name.
     */
    public function getMonthNameAttribute()
    {
        return Carbon::create($this->year, $this->month, 1)->format('F Y');
    }

    /**
     * Get weekend day names.
     */
    public function getWeekendDayNamesAttribute()
    {
        $dayNames = [
            0 => 'Sunday',
            1 => 'Monday', 
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday'
        ];

        return collect($this->weekend_days)->map(function($day) use ($dayNames) {
            return $dayNames[$day] ?? 'Unknown';
        })->toArray();
    }

    /**
     * Check if a specific day of week is a weekend.
     */
    public function isWeekendDay($dayOfWeek)
    {
        return in_array($dayOfWeek, $this->weekend_days);
    }

    /**
     * Get all configurations ordered by year and month.
     */
    public static function getAllOrdered()
    {
        return self::orderBy('year', 'desc')
                  ->orderBy('month', 'desc')
                  ->get();
    }

    /**
     * Get configurations for a specific year.
     */
    public static function getForYear($year)
    {
        return self::where('year', $year)
                  ->orderBy('month')
                  ->get();
    }

    /**
     * Deactivate configuration.
     */
    public function deactivate()
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Activate configuration.
     */
    public function activate()
    {
        $this->update(['is_active' => true]);
    }
}
