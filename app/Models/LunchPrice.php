<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LunchPrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'year',
        'month',
        'price',
    ];

    protected $casts = [
        'price' => 'decimal:2',
    ];

    /**
     * Get price for a specific month and year.
     */
    public static function getPriceForMonth($year, $month)
    {
        $price = self::where('year', $year)
                    ->where('month', $month)
                    ->first();

        return $price ? $price->price : 5.00; // Default price if not set
    }

    /**
     * Set price for a specific month and year.
     */
    public static function setPriceForMonth($year, $month, $price)
    {
        return self::updateOrCreate(
            ['year' => $year, 'month' => $month],
            ['price' => $price]
        );
    }

    /**
     * Scope to get price for a specific month and year.
     */
    public function scopeForMonth($query, $year, $month)
    {
        return $query->where('year', $year)->where('month', $month);
    }
}
