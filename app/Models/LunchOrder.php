<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class LunchOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_date',
        'status',
    ];

    protected $casts = [
        'order_date' => 'date',
    ];

    /**
     * Get the user that owns the lunch order.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get orders for a specific date.
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('order_date', $date);
    }

    /**
     * Scope to get ordered lunches (not cancelled).
     */
    public function scopeOrdered($query)
    {
        return $query->where('status', 'ordered');
    }

    /**
     * Scope to get cancelled lunches.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope to get orders for a specific month and year.
     */
    public function scopeForMonth($query, $year, $month)
    {
        return $query->whereYear('order_date', $year)
                    ->whereMonth('order_date', $month);
    }

    /**
     * Check if the order is ordered.
     */
    public function isOrdered(): bool
    {
        return $this->status === 'ordered';
    }

    /**
     * Check if the order is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Cancel the order.
     */
    public function cancel()
    {
        $this->update(['status' => 'cancelled']);
    }

    /**
     * Reorder (change status back to ordered).
     */
    public function reorder()
    {
        $this->update(['status' => 'ordered']);
    }
}
