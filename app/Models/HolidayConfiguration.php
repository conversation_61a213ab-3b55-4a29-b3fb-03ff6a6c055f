<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class HolidayConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'year',
        'month',
        'holiday_date',
        'name',
        'description',
        'type',
        'is_active',
    ];

    protected $casts = [
        'holiday_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get active configurations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get configuration for a specific month.
     */
    public function scopeForMonth($query, $year, $month)
    {
        return $query->where('year', $year)->where('month', $month);
    }

    /**
     * Scope to get configuration for a specific date.
     */
    public function scopeForDate($query, $date)
    {
        $carbonDate = Carbon::parse($date);
        return $query->where('holiday_date', $carbonDate->format('Y-m-d'));
    }

    /**
     * Scope to get configuration by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get holidays for a specific month.
     */
    public static function getForMonth($year, $month)
    {
        return self::active()
                  ->forMonth($year, $month)
                  ->orderBy('holiday_date')
                  ->get();
    }

    /**
     * Get holiday dates for a specific month.
     */
    public static function getHolidayDatesForMonth($year, $month)
    {
        return self::getForMonth($year, $month)
                  ->pluck('holiday_date')
                  ->map(function($date) {
                      return $date->format('Y-m-d');
                  })
                  ->toArray();
    }

    /**
     * Check if a specific date is a holiday.
     */
    public static function isHoliday($date)
    {
        $carbonDate = Carbon::parse($date);
        
        return self::active()
                  ->forDate($carbonDate)
                  ->exists();
    }

    /**
     * Add holiday for a specific date.
     */
    public static function addHoliday($date, $name, $description = null, $type = 'custom')
    {
        $carbonDate = Carbon::parse($date);
        
        return self::updateOrCreate(
            [
                'holiday_date' => $carbonDate->format('Y-m-d'),
                'year' => $carbonDate->year,
                'month' => $carbonDate->month,
            ],
            [
                'name' => $name,
                'description' => $description,
                'type' => $type,
                'is_active' => true,
            ]
        );
    }

    /**
     * Get formatted date.
     */
    public function getFormattedDateAttribute()
    {
        return $this->holiday_date->format('F j, Y');
    }

    /**
     * Get formatted short date.
     */
    public function getShortDateAttribute()
    {
        return $this->holiday_date->format('M j');
    }

    /**
     * Get day of week.
     */
    public function getDayOfWeekAttribute()
    {
        return $this->holiday_date->format('l');
    }

    /**
     * Get all holidays ordered by date.
     */
    public static function getAllOrdered()
    {
        return self::orderBy('holiday_date', 'desc')
                  ->get();
    }

    /**
     * Get holidays for a specific year.
     */
    public static function getForYear($year)
    {
        return self::where('year', $year)
                  ->orderBy('holiday_date')
                  ->get();
    }

    /**
     * Get upcoming holidays.
     */
    public static function getUpcoming($limit = 5)
    {
        return self::active()
                  ->where('holiday_date', '>=', Carbon::today())
                  ->orderBy('holiday_date')
                  ->limit($limit)
                  ->get();
    }

    /**
     * Get holidays by type.
     */
    public static function getByType($type)
    {
        return self::active()
                  ->byType($type)
                  ->orderBy('holiday_date')
                  ->get();
    }

    /**
     * Deactivate holiday.
     */
    public function deactivate()
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Activate holiday.
     */
    public function activate()
    {
        $this->update(['is_active' => true]);
    }

    /**
     * Check if holiday is in the past.
     */
    public function isPast()
    {
        return $this->holiday_date->isPast();
    }

    /**
     * Check if holiday is today.
     */
    public function isToday()
    {
        return $this->holiday_date->isToday();
    }

    /**
     * Check if holiday is in the future.
     */
    public function isFuture()
    {
        return $this->holiday_date->isFuture();
    }
}
