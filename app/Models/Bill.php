<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bill extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'year',
        'month',
        'lunch_count',
        'rate',
        'amount',
    ];

    protected $casts = [
        'rate' => 'decimal:2',
        'amount' => 'decimal:2',
    ];

    /**
     * Get the user that owns the bill.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get bills for a specific month and year.
     */
    public function scopeForMonth($query, $year, $month)
    {
        return $query->where('year', $year)->where('month', $month);
    }

    /**
     * Generate bill for a user for a specific month.
     */
    public static function generateForUser(User $user, $year, $month)
    {
        // Count ordered lunches for the month
        $lunchCount = $user->lunchOrders()
                          ->forMonth($year, $month)
                          ->ordered()
                          ->count();

        // Get the rate for the month
        $rate = LunchPrice::getPriceForMonth($year, $month);

        // Calculate amount
        $amount = $lunchCount * $rate;

        // Create or update the bill
        return self::updateOrCreate(
            [
                'user_id' => $user->id,
                'year' => $year,
                'month' => $month,
            ],
            [
                'lunch_count' => $lunchCount,
                'rate' => $rate,
                'amount' => $amount,
            ]
        );
    }

    /**
     * Get formatted month name.
     */
    public function getMonthNameAttribute()
    {
        return date('F', mktime(0, 0, 0, $this->month, 1));
    }
}
