<?php

namespace App\Http\Controllers;

use App\Services\LunchOrderService;
use App\Services\BillingService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class LunchController extends Controller
{
    protected $lunchOrderService;
    protected $billingService;

    public function __construct(LunchOrderService $lunchOrderService, BillingService $billingService)
    {
        $this->lunchOrderService = $lunchOrderService;
        $this->billingService = $billingService;
    }

    /**
     * Show the lunch dashboard.
     */
    public function index()
    {
        $user = auth()->user();
        $today = Carbon::today();

        $todayOrder = $this->lunchOrderService->getUserOrderForDate($user, $today);
        $hasOrderedToday = $this->lunchOrderService->hasUserOrderedForDate($user, $today);

        // Get current month bill
        $currentBill = $this->billingService->getBillForUser($user);

        // Check if today is a valid ordering day
        $orderingStatus = $this->lunchOrderService->canOrderToday();

        // Add time remaining information if ordering is still available
        if ($orderingStatus['can_order'] && !($orderingStatus['is_past_deadline'] ?? false)) {
            $orderingStatus['time_remaining'] = $this->lunchOrderService->getTimeUntilDeadline();
        }

        // Get upcoming orders summary
        $upcomingSummary = $this->lunchOrderService->getUpcomingOrdersSummary($user);

        // Get tomorrow's order status
        $tomorrow = Carbon::tomorrow();
        $tomorrowOrder = $this->lunchOrderService->getUserOrderForDate($user, $tomorrow);
        $hasTomorrowOrder = $tomorrowOrder && $tomorrowOrder->isOrdered();
        $tomorrowOrderingStatus = $this->lunchOrderService->canOrderTomorrow($user);

        // Check if tomorrow's order can be cancelled
        $canCancelTomorrow = false;
        $tomorrowCancelReason = '';
        if ($tomorrowOrder) {
            $cancelStatus = $this->lunchOrderService->canCancelOrder($user, $tomorrow);
            $canCancelTomorrow = $cancelStatus['can_cancel'];
            $tomorrowCancelReason = $cancelStatus['reason'];
        }

        return view('lunch.index', compact(
            'user', 'todayOrder', 'hasOrderedToday', 'currentBill', 'orderingStatus', 'upcomingSummary',
            'tomorrow', 'tomorrowOrder', 'hasTomorrowOrder', 'tomorrowOrderingStatus',
            'canCancelTomorrow', 'tomorrowCancelReason'
        ));
    }

    /**
     * Place lunch order.
     */
    public function placeOrder(Request $request)
    {
        $user = auth()->user();
        $date = $request->input('date', Carbon::today());

        try {
            $order = $this->lunchOrderService->placeOrder($user, $date);

            return response()->json([
                'success' => true,
                'message' => 'Lunch order placed successfully!',
                'order' => $order
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Cancel lunch order.
     */
    public function cancelOrder(Request $request)
    {
        $user = auth()->user();
        $date = $request->input('date', Carbon::today());

        try {
            $cancelled = $this->lunchOrderService->cancelOrder($user, $date);

            if ($cancelled) {
                return response()->json([
                    'success' => true,
                    'message' => 'Lunch order cancelled successfully!'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'No order found to cancel.'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get user's lunch history.
     */
    public function history(Request $request)
    {
        $user = auth()->user();
        $month = $request->input('month', Carbon::now()->month);
        $year = $request->input('year', Carbon::now()->year);
        
        $orders = $user->lunchOrders()
                      ->forMonth($year, $month)
                      ->orderBy('order_date', 'desc')
                      ->get();
        
        $bill = $this->billingService->getBillForUser($user, $year, $month);
        
        return view('lunch.history', compact('orders', 'bill', 'month', 'year'));
    }

    /**
     * Get today's lunch summary.
     */
    public function todaySummary()
    {
        $orderedLunches = $this->lunchOrderService->getOrdersForDate();
        $cancelledLunches = $this->lunchOrderService->getCancelledOrdersForDate();

        return response()->json([
            'ordered' => $orderedLunches,
            'cancelled' => $cancelledLunches,
            'total_ordered' => $orderedLunches->count(),
            'total_cancelled' => $cancelledLunches->count()
        ]);
    }

    /**
     * Show upcoming lunch orders.
     */
    public function upcoming()
    {
        $user = auth()->user();
        $upcomingOrders = $this->lunchOrderService->getUpcomingOrders($user);

        // Add cancellation status for each order
        $ordersWithStatus = $upcomingOrders->map(function ($order) use ($user) {
            $cancelStatus = $this->lunchOrderService->canCancelOrder($user, $order->order_date);
            $order->can_cancel = $cancelStatus['can_cancel'];
            $order->cancel_reason = $cancelStatus['reason'];
            $order->is_business_day = app(\App\Services\BusinessDayService::class)->isBusinessDay($order->order_date);
            return $order;
        });

        return view('lunch.upcoming', compact('user', 'ordersWithStatus'));
    }

    /**
     * Place order for a specific future date.
     */
    public function placeOrderForDate(Request $request)
    {
        $request->validate([
            'date' => 'required|date|after:today'
        ]);

        $user = auth()->user();
        $date = Carbon::parse($request->date);

        try {
            $order = $this->lunchOrderService->placeOrder($user, $date);

            return response()->json([
                'success' => true,
                'message' => "Lunch order placed for {$date->format('l, F j, Y')}!",
                'order' => $order
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Cancel order for a specific date.
     */
    public function cancelOrderForDate(Request $request)
    {
        $request->validate([
            'date' => 'required|date'
        ]);

        $user = auth()->user();
        $date = Carbon::parse($request->date);

        try {
            $cancelled = $this->lunchOrderService->cancelOrder($user, $date);

            if ($cancelled) {
                return response()->json([
                    'success' => true,
                    'message' => "Lunch order cancelled for {$date->format('l, F j, Y')}!"
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'No order found to cancel.'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Place order for tomorrow.
     */
    public function placeTomorrowOrder()
    {
        $user = auth()->user();
        $tomorrow = Carbon::tomorrow();

        try {
            $order = $this->lunchOrderService->placeOrder($user, $tomorrow);

            return response()->json([
                'success' => true,
                'message' => "Lunch order placed for {$tomorrow->format('l, F j, Y')}!",
                'order' => $order
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Cancel tomorrow's order.
     */
    public function cancelTomorrowOrder()
    {
        $user = auth()->user();
        $tomorrow = Carbon::tomorrow();

        try {
            $cancelled = $this->lunchOrderService->cancelOrder($user, $tomorrow);

            if ($cancelled) {
                return response()->json([
                    'success' => true,
                    'message' => "Tomorrow's lunch order cancelled!"
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'No order found to cancel.'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
