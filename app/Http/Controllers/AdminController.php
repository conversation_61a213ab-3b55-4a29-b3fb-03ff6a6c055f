<?php

namespace App\Http\Controllers;

use App\Services\LunchOrderService;
use App\Services\BillingService;
use App\Services\UserService;
use App\Services\BusinessDayService;
use App\Http\Requests\WeekendConfigurationRequest;
use App\Http\Requests\HolidayConfigurationRequest;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AdminController extends Controller
{
    protected $lunchOrderService;
    protected $billingService;
    protected $userService;
    protected $businessDayService;

    public function __construct(
        LunchOrderService $lunchOrderService,
        BillingService $billingService,
        UserService $userService,
        BusinessDayService $businessDayService
    ) {
        $this->lunchOrderService = $lunchOrderService;
        $this->billingService = $billingService;
        $this->userService = $userService;
        $this->businessDayService = $businessDayService;
    }

    /**
     * Show admin dashboard.
     */
    public function index()
    {
        $todayOrders = $this->lunchOrderService->getOrdersForDate();
        $todayCancelled = $this->lunchOrderService->getCancelledOrdersForDate();
        $currentMonthSummary = $this->billingService->getMonthlySummary();
        
        return view('admin.index', compact('todayOrders', 'todayCancelled', 'currentMonthSummary'));
    }

    /**
     * Show users management.
     */
    public function users()
    {
        $users = $this->userService->getAllUsersWithStats();
        return view('admin.users', compact('users'));
    }

    /**
     * Create a new user.
     */
    public function createUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'role' => 'required|in:admin,user',
            'mode' => 'required|in:regular,guest',
            'status' => 'required|in:active,inactive',
        ]);

        $user = $this->userService->createUser(
            $request->name,
            $request->email,
            $request->password,
            $request->role,
            $request->mode,
            $request->status
        );

        return response()->json([
            'success' => true,
            'message' => 'User created successfully!',
            'user' => $user
        ]);
    }

    /**
     * Update user mode.
     */
    public function updateUserMode(Request $request, $userId)
    {
        $request->validate([
            'mode' => 'required|in:regular,guest',
        ]);

        $user = \App\Models\User::findOrFail($userId);
        $this->userService->updateUserMode($user, $request->mode);

        return response()->json([
            'success' => true,
            'message' => 'User mode updated successfully!'
        ]);
    }

    /**
     * Update user status.
     */
    public function updateUserStatus(Request $request, $userId)
    {
        $request->validate([
            'status' => 'required|in:active,inactive',
        ]);

        $user = \App\Models\User::findOrFail($userId);
        $this->userService->updateUserStatus($user, $request->status);

        return response()->json([
            'success' => true,
            'message' => 'User status updated successfully!'
        ]);
    }

    /**
     * Show the form for editing a user.
     */
    public function editUser($userId)
    {
        $user = \App\Models\User::findOrFail($userId);
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update a user comprehensively.
     */
    public function updateUser(Request $request, $userId)
    {
        $user = \App\Models\User::findOrFail($userId);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8',
            'role' => 'required|in:admin,user',
            'mode' => 'required|in:regular,guest',
            'status' => 'required|in:active,inactive',
        ]);

        $this->userService->updateUser($user, $request->all());

        return redirect()->route('admin.users')->with('success', 'User updated successfully!');
    }

    /**
     * Show lunch orders for a specific date.
     */
    public function lunchOrders(Request $request)
    {
        $date = $request->input('date', Carbon::today());
        $orderedLunches = $this->lunchOrderService->getOrdersForDate($date);
        $cancelledLunches = $this->lunchOrderService->getCancelledOrdersForDate($date);
        
        return view('admin.lunch-orders', compact('orderedLunches', 'cancelledLunches', 'date'));
    }

    /**
     * Show lunch pricing management.
     */
    public function pricing()
    {
        $prices = $this->billingService->getAllLunchPrices();
        return view('admin.pricing', compact('prices'));
    }

    /**
     * Set lunch price for a month.
     */
    public function setPrice(Request $request)
    {
        $request->validate([
            'year' => 'required|integer|min:2020|max:2030',
            'month' => 'required|integer|min:1|max:12',
            'price' => 'required|numeric|min:0',
        ]);

        $this->billingService->setLunchPrice(
            $request->year,
            $request->month,
            $request->price
        );

        return response()->json([
            'success' => true,
            'message' => 'Lunch price set successfully!'
        ]);
    }

    /**
     * Show billing management.
     */
    public function billing(Request $request)
    {
        $year = $request->input('year', Carbon::now()->year);
        $month = $request->input('month', Carbon::now()->month);
        
        $summary = $this->billingService->getMonthlySummary($year, $month);
        
        return view('admin.billing', compact('summary'));
    }

    /**
     * Generate bills for a month.
     */
    public function generateBills(Request $request)
    {
        $request->validate([
            'year' => 'required|integer|min:2020|max:2030',
            'month' => 'required|integer|min:1|max:12',
        ]);

        $billsGenerated = $this->billingService->generateBillsForMonth(
            $request->year,
            $request->month
        );

        return response()->json([
            'success' => true,
            'message' => "Generated {$billsGenerated} bills successfully!"
        ]);
    }

    /**
     * Show business day management.
     */
    public function businessDays(Request $request)
    {
        $year = $request->input('year', Carbon::now()->year);
        $month = $request->input('month', Carbon::now()->month);

        $monthSummary = $this->lunchOrderService->getMonthSummaryWithBusinessDays($year, $month);

        return view('admin.business-days', compact('monthSummary'));
    }

    /**
     * Create auto orders for all business days in a month.
     */
    public function createMonthlyAutoOrders(Request $request)
    {
        $request->validate([
            'year' => 'required|integer|min:2020|max:2030',
            'month' => 'required|integer|min:1|max:12',
        ]);

        $ordersCreated = $this->lunchOrderService->createAutoOrdersForMonth(
            $request->year,
            $request->month
        );

        return response()->json([
            'success' => true,
            'message' => "Created {$ordersCreated} auto orders for all business days!"
        ]);
    }

    /**
     * Show weekend and holiday configuration management.
     */
    public function weekendHolidayConfig(Request $request)
    {
        $year = $request->input('year', Carbon::now()->year);
        $month = $request->input('month', Carbon::now()->month);

        $monthSummary = $this->lunchOrderService->getMonthSummaryWithBusinessDays($year, $month);
        $weekendConfig = $this->businessDayService->getWeekendConfiguration($year, $month);
        $holidayConfigs = $this->businessDayService->getHolidayConfiguration($year, $month);

        return view('admin.weekend-holiday-config', compact('monthSummary', 'weekendConfig', 'holidayConfigs'));
    }

    /**
     * Show dedicated weekend configuration management.
     */
    public function weekendConfig(Request $request)
    {
        $selectedYear = $request->input('year', Carbon::now()->year);
        $weekendConfigs = $this->businessDayService->getWeekendConfigurationsForYear($selectedYear);

        return view('admin.weekend-config', compact('selectedYear', 'weekendConfigs'));
    }

    /**
     * Set weekend configuration for a month.
     */
    public function setWeekendConfig(WeekendConfigurationRequest $request)
    {
        try {
            // Basic validation
            if (count($request->weekend_days) >= 7) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot set all days as weekends. At least one business day is required.'
                ], 422);
            }

            $config = $this->businessDayService->setWeekendConfiguration(
                $request->year,
                $request->month,
                $request->weekend_days,
                $request->description
            );

            return response()->json([
                'success' => true,
                'message' => 'Weekend configuration updated successfully!',
                'config' => $config
            ]);

        } catch (\Exception $e) {
            \Log::error('Weekend configuration error: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating weekend configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add holiday configuration.
     */
    public function addHoliday(HolidayConfigurationRequest $request)
    {
        try {
            $holiday = $this->businessDayService->addHoliday(
                $request->holiday_date,
                $request->name,
                $request->description,
                $request->type
            );

            return response()->json([
                'success' => true,
                'message' => 'Holiday added successfully!',
                'holiday' => $holiday
            ]);

        } catch (\Exception $e) {
            \Log::error('Holiday configuration error: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while adding holiday: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove holiday configuration.
     */
    public function removeHoliday(Request $request)
    {
        $request->validate([
            'holiday_id' => 'required|integer|exists:holiday_configurations,id',
        ]);

        $removed = $this->businessDayService->removeHoliday($request->holiday_id);

        if ($removed) {
            return response()->json([
                'success' => true,
                'message' => 'Holiday removed successfully!'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to remove holiday.'
        ], 400);
    }

    /**
     * Set weekend configuration for multiple months (bulk operation).
     */
    public function setBulkWeekendConfig(Request $request)
    {
        try {
            // Debug logging
            \Log::info('Bulk weekend config request received', [
                'request_data' => $request->all(),
                'content_type' => $request->header('Content-Type'),
                'method' => $request->method()
            ]);

            // Validate the basic fields first
            try {
                $request->validate([
                    'apply_to' => 'required|in:current_year,next_year,both_years,custom_range',
                    'bulk_description' => 'nullable|string|max:255',
                    'start_month' => 'required_if:apply_to,custom_range|nullable|date_format:Y-m',
                    'end_month' => 'required_if:apply_to,custom_range|nullable|date_format:Y-m',
                ]);
            } catch (\Illuminate\Validation\ValidationException $e) {
                \Log::error('Basic validation failed', [
                    'errors' => $e->errors(),
                    'request_data' => $request->all()
                ]);
                throw $e;
            }

            // Check if weekend_days exists and is valid
            if (!$request->has('weekend_days') || !is_array($request->weekend_days)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Weekend days are required and must be an array.'
                ], 422);
            }

            // Validate weekend days
            foreach ($request->weekend_days as $day) {
                if (!is_numeric($day) || $day < 0 || $day > 6) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid weekend day value. Must be between 0 and 6.'
                    ], 422);
                }
            }

            // Validate weekend days array
            if (count($request->weekend_days) >= 7) {
                \Log::error('Bulk config error: Too many weekend days', [
                    'weekend_days' => $request->weekend_days,
                    'count' => count($request->weekend_days)
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot set all days as weekends. At least one business day is required.'
                ], 422);
            }

            \Log::info('Bulk config validation passed', [
                'weekend_days' => $request->weekend_days,
                'apply_to' => $request->apply_to
            ]);

            $currentYear = Carbon::now()->year;
            $monthsToUpdate = [];

            switch ($request->apply_to) {
                case 'current_year':
                    for ($month = 1; $month <= 12; $month++) {
                        $monthsToUpdate[] = ['year' => $currentYear, 'month' => $month];
                    }
                    break;

                case 'next_year':
                    for ($month = 1; $month <= 12; $month++) {
                        $monthsToUpdate[] = ['year' => $currentYear + 1, 'month' => $month];
                    }
                    break;

                case 'both_years':
                    for ($year = $currentYear; $year <= $currentYear + 1; $year++) {
                        for ($month = 1; $month <= 12; $month++) {
                            $monthsToUpdate[] = ['year' => $year, 'month' => $month];
                        }
                    }
                    break;

                case 'custom_range':
                    if (!$request->start_month || !$request->end_month) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Start month and end month are required for custom range.'
                        ], 422);
                    }

                    $startDate = Carbon::createFromFormat('Y-m', $request->start_month);
                    $endDate = Carbon::createFromFormat('Y-m', $request->end_month);

                    if (!$startDate || !$endDate) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Invalid date format for custom range.'
                        ], 422);
                    }

                    if ($startDate->gt($endDate)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Start month cannot be after end month.'
                        ], 422);
                    }

                    $current = $startDate->copy();
                    while ($current->lte($endDate)) {
                        $monthsToUpdate[] = ['year' => $current->year, 'month' => $current->month];
                        $current->addMonth();
                    }
                    break;
            }

            if (empty($monthsToUpdate)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No months to update.'
                ], 422);
            }

            $updatedCount = 0;
            $errors = [];

            foreach ($monthsToUpdate as $monthData) {
                try {
                    $this->businessDayService->setWeekendConfiguration(
                        $monthData['year'],
                        $monthData['month'],
                        $request->weekend_days,
                        $request->bulk_description
                    );
                    $updatedCount++;
                } catch (\Exception $e) {
                    $errors[] = "Failed to update {$monthData['year']}-{$monthData['month']}: " . $e->getMessage();
                }
            }

            if (!empty($errors)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Some configurations failed to update.',
                    'errors' => $errors,
                    'updated_count' => $updatedCount
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => "Updated weekend configuration for {$updatedCount} months successfully!"
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Bulk weekend config validation error', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);

            $errorMessages = [];
            foreach ($e->errors() as $field => $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Bulk weekend configuration error: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk import holidays.
     */
    public function bulkImportHolidays(Request $request)
    {
        $request->validate([
            'import_method' => 'required|in:csv,manual,year_template',
            'csv_file' => 'required_if:import_method,csv|file|mimes:csv,txt',
            'holiday_list' => 'required_if:import_method,manual|string',
            'target_year' => 'required_if:import_method,year_template|integer|min:2020|max:2030',
        ]);

        $importedCount = 0;
        $errors = [];

        try {
            switch ($request->import_method) {
                case 'csv':
                    $importedCount = $this->importHolidaysFromCsv($request->file('csv_file'));
                    break;

                case 'manual':
                    $importedCount = $this->importHolidaysFromText($request->holiday_list);
                    break;

                case 'year_template':
                    $importedCount = $this->applyHolidaysToYear($request->target_year);
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully imported {$importedCount} holidays!"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Import holidays from CSV file.
     */
    private function importHolidaysFromCsv($file)
    {
        $count = 0;
        $handle = fopen($file->getPathname(), 'r');

        // Skip header row if exists
        $firstLine = fgetcsv($handle);
        if (!$this->isValidHolidayRow($firstLine)) {
            fseek($handle, 0); // Reset to beginning if first line is data
        }

        while (($row = fgetcsv($handle)) !== false) {
            if (count($row) >= 2 && $this->isValidHolidayRow($row)) {
                $date = $row[0];
                $name = $row[1];
                $description = $row[2] ?? '';
                $type = $row[3] ?? 'custom';

                $this->businessDayService->addHoliday($date, $name, $description, $type);
                $count++;
            }
        }

        fclose($handle);
        return $count;
    }

    /**
     * Import holidays from text input.
     */
    private function importHolidaysFromText($text)
    {
        $count = 0;
        $lines = explode("\n", $text);

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            $parts = str_getcsv($line);
            if (count($parts) >= 2 && $this->isValidHolidayRow($parts)) {
                $date = $parts[0];
                $name = $parts[1];
                $description = $parts[2] ?? '';
                $type = $parts[3] ?? 'custom';

                $this->businessDayService->addHoliday($date, $name, $description, $type);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Apply current month's holidays to entire year.
     */
    private function applyHolidaysToYear($targetYear)
    {
        $currentYear = Carbon::now()->year;
        $currentMonth = Carbon::now()->month;

        // Get holidays from current month
        $sourceHolidays = $this->businessDayService->getHolidayConfiguration($currentYear, $currentMonth);
        $count = 0;

        foreach ($sourceHolidays as $holiday) {
            // Apply to same month in target year
            $newDate = Carbon::parse($holiday->holiday_date)->year($targetYear);

            $this->businessDayService->addHoliday(
                $newDate->format('Y-m-d'),
                $holiday->name,
                "Applied from {$currentYear}-{$currentMonth} template",
                $holiday->type
            );
            $count++;
        }

        return $count;
    }

    /**
     * Validate if a CSV row contains valid holiday data.
     */
    private function isValidHolidayRow($row)
    {
        if (count($row) < 2) return false;

        // Check if first column looks like a date
        $date = $row[0];
        return preg_match('/^\d{4}-\d{2}-\d{2}$/', $date) && !empty($row[1]);
    }

    /**
     * Validate weekend configuration without saving.
     */
    public function validateWeekendConfig(Request $request)
    {
        $request->validate([
            'year' => 'required|integer|min:2020|max:2030',
            'month' => 'required|integer|min:1|max:12',
            'weekend_days' => 'required|array|min:1',
            'weekend_days.*' => 'integer|min:0|max:6',
        ]);

        // Basic validation
        $issues = [];
        $warnings = [];

        if (count($request->weekend_days) >= 7) {
            $issues[] = 'Cannot set all days as weekends. At least one business day is required.';
        }

        if (count($request->weekend_days) === 1) {
            $warnings[] = 'Only one weekend day selected. This is unusual.';
        }

        return response()->json([
            'valid' => empty($issues),
            'issues' => $issues,
            'warnings' => $warnings
        ]);
    }

    /**
     * Debug bulk configuration request.
     */
    public function debugBulkConfig(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'Debug info',
            'request_all' => $request->all(),
            'weekend_days' => $request->weekend_days,
            'weekend_days_type' => gettype($request->weekend_days),
            'weekend_days_count' => is_array($request->weekend_days) ? count($request->weekend_days) : 'not array',
            'apply_to' => $request->apply_to,
            'headers' => $request->headers->all()
        ]);
    }

    /**
     * Validate holiday configuration without saving.
     */
    public function validateHolidayConfig(Request $request)
    {
        $request->validate([
            'holiday_date' => 'required|date',
            'name' => 'required|string|max:255',
            'type' => 'required|in:fixed,variable,custom',
        ]);

        // Basic validation
        $issues = [];
        $warnings = [];

        // Check for duplicate holidays
        $date = Carbon::parse($request->holiday_date);
        $existingHoliday = \App\Models\HolidayConfiguration::where('holiday_date', $date->format('Y-m-d'))
            ->where('is_active', true)
            ->first();

        if ($existingHoliday) {
            $issues[] = "A holiday already exists on {$date->format('F j, Y')}: {$existingHoliday->name}";
        }

        return response()->json([
            'valid' => empty($issues),
            'issues' => $issues,
            'warnings' => $warnings
        ]);
    }
}
