<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class HolidayConfigurationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->role === 'admin';
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'holiday_date' => [
                'required',
                'date',
                'after_or_equal:2020-01-01',
                'before_or_equal:2030-12-31',
            ],
            'name' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'description' => [
                'nullable',
                'string',
                'max:500',
            ],
            'type' => [
                'required',
                'in:fixed,variable,custom',
            ],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'holiday_date.required' => 'Holiday date is required.',
            'holiday_date.date' => 'Holiday date must be a valid date.',
            'holiday_date.after_or_equal' => 'Holiday date must be after or equal to January 1, 2020.',
            'holiday_date.before_or_equal' => 'Holiday date must be before or equal to December 31, 2030.',
            'name.required' => 'Holiday name is required.',
            'name.string' => 'Holiday name must be a valid string.',
            'name.min' => 'Holiday name must be at least 2 characters.',
            'name.max' => 'Holiday name cannot exceed 255 characters.',
            'description.string' => 'Description must be a valid string.',
            'description.max' => 'Description cannot exceed 500 characters.',
            'type.required' => 'Holiday type is required.',
            'type.in' => 'Holiday type must be one of: fixed, variable, custom.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $holidayDate = $this->holiday_date;
            
            if ($holidayDate) {
                $date = Carbon::parse($holidayDate);
                
                // Check for duplicate holidays on the same date
                $existingHoliday = \App\Models\HolidayConfiguration::where('holiday_date', $date->format('Y-m-d'))
                    ->where('is_active', true)
                    ->first();
                
                if ($existingHoliday) {
                    $validator->errors()->add('holiday_date', "A holiday already exists on {$date->format('F j, Y')}: {$existingHoliday->name}");
                }

                // Validate business logic for holiday types
                $type = $this->type;
                $name = $this->name;
                
                if ($type === 'fixed' && $name) {
                    // For fixed holidays, check if the name suggests it should be on a specific date
                    $fixedHolidayDates = [
                        'new year' => '01-01',
                        'christmas' => '12-25',
                        'independence day' => '07-04',
                        'valentine' => '02-14',
                        'halloween' => '10-31',
                    ];
                    
                    foreach ($fixedHolidayDates as $holidayName => $expectedDate) {
                        if (stripos($name, $holidayName) !== false) {
                            $expectedMonthDay = $expectedDate;
                            $actualMonthDay = $date->format('m-d');
                            
                            if ($actualMonthDay !== $expectedMonthDay) {
                                $validator->errors()->add('holiday_date', "Warning: {$name} is typically celebrated on {$expectedDate}, but you've set it for {$actualMonthDay}.");
                            }
                        }
                    }
                }

                // Check if holiday falls on an already configured weekend
                $weekendConfig = \App\Models\WeekendConfiguration::getForMonth($date->year, $date->month);
                $weekendDays = $weekendConfig ? $weekendConfig->weekend_days : [0, 6];
                
                if (in_array($date->dayOfWeek, $weekendDays)) {
                    $validator->errors()->add('holiday_date', "Warning: {$date->format('F j, Y')} falls on a weekend day. Consider if this holiday is necessary.");
                }

                // Validate reasonable holiday frequency
                $monthHolidayCount = \App\Models\HolidayConfiguration::where('year', $date->year)
                    ->where('month', $date->month)
                    ->where('is_active', true)
                    ->count();
                
                if ($monthHolidayCount >= 5) {
                    $validator->errors()->add('holiday_date', "Warning: This month already has {$monthHolidayCount} holidays. Consider if another holiday is necessary.");
                }

                // Check for holidays too close together
                $nearbyHolidays = \App\Models\HolidayConfiguration::where('holiday_date', '>=', $date->subDays(3)->format('Y-m-d'))
                    ->where('holiday_date', '<=', $date->addDays(6)->format('Y-m-d'))
                    ->where('is_active', true)
                    ->count();
                
                if ($nearbyHolidays > 0) {
                    $validator->errors()->add('holiday_date', "Warning: There are other holidays within 3 days of this date. This may create an extended break period.");
                }
            }
        });
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'holiday_date' => 'holiday date',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        if ($this->expectsJson()) {
            $response = response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $validator->errors()
            ], 422);

            throw new \Illuminate\Http\Exceptions\HttpResponseException($response);
        }

        parent::failedValidation($validator);
    }
}
