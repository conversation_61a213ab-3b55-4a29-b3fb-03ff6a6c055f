<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WeekendConfigurationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->role === 'admin';
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'year' => [
                'required',
                'integer',
                'min:2020',
                'max:2030',
            ],
            'month' => [
                'required',
                'integer',
                'min:1',
                'max:12',
            ],
            'weekend_days' => [
                'required',
                'array',
                'min:1',
                'max:7',
            ],
            'weekend_days.*' => [
                'integer',
                'min:0',
                'max:6',
                'distinct',
            ],
            'description' => [
                'nullable',
                'string',
                'max:255',
            ],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'year.required' => 'Year is required.',
            'year.integer' => 'Year must be a valid integer.',
            'year.min' => 'Year must be at least 2020.',
            'year.max' => 'Year cannot be greater than 2030.',
            'month.required' => 'Month is required.',
            'month.integer' => 'Month must be a valid integer.',
            'month.min' => 'Month must be between 1 and 12.',
            'month.max' => 'Month must be between 1 and 12.',
            'weekend_days.required' => 'At least one weekend day must be selected.',
            'weekend_days.array' => 'Weekend days must be provided as an array.',
            'weekend_days.min' => 'At least one weekend day must be selected.',
            'weekend_days.max' => 'Cannot select more than 7 weekend days.',
            'weekend_days.*.integer' => 'Weekend day must be a valid day number.',
            'weekend_days.*.min' => 'Weekend day must be between 0 and 6.',
            'weekend_days.*.max' => 'Weekend day must be between 0 and 6.',
            'weekend_days.*.distinct' => 'Duplicate weekend days are not allowed.',
            'description.string' => 'Description must be a valid string.',
            'description.max' => 'Description cannot exceed 255 characters.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate that not all days are weekends
            if (count($this->weekend_days) >= 7) {
                $validator->errors()->add('weekend_days', 'Cannot set all days as weekends. At least one business day is required.');
            }

            // Validate date range
            $year = $this->year;
            $month = $this->month;
            
            if ($year && $month) {
                // Check if the month/year combination is valid
                if (!checkdate($month, 1, $year)) {
                    $validator->errors()->add('month', 'Invalid month/year combination.');
                }

                // Warn if configuring past dates (but don't prevent it)
                $configDate = \Carbon\Carbon::create($year, $month, 1);
                if ($configDate->isPast() && $configDate->diffInMonths(now()) > 12) {
                    $validator->errors()->add('year', 'Warning: Configuring weekends for dates more than 12 months in the past.');
                }

                // Warn if configuring too far in the future
                if ($configDate->isFuture() && $configDate->diffInMonths(now()) > 24) {
                    $validator->errors()->add('year', 'Warning: Configuring weekends for dates more than 24 months in the future.');
                }
            }
        });
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'weekend_days' => 'weekend days',
            'weekend_days.*' => 'weekend day',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        if ($this->expectsJson()) {
            $response = response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $validator->errors()
            ], 422);

            throw new \Illuminate\Http\Exceptions\HttpResponseException($response);
        }

        parent::failedValidation($validator);
    }
}
