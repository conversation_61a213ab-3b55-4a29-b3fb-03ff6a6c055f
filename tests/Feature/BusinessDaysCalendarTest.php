<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\WeekendConfiguration;
use App\Models\HolidayConfiguration;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BusinessDaysCalendarTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user for testing
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'mode' => 'regular',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function business_days_calendar_displays_traditional_calendar_layout()
    {
        // Set up a specific month for testing
        $year = 2024;
        $month = 1; // January 2024

        // Configure weekends (Saturday and Sunday)
        WeekendConfiguration::setForMonth($year, $month, [0, 6]); // Sunday and Saturday

        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.business-days', ['year' => $year, 'month' => $month]));

        $response->assertStatus(200);

        // Check that the calendar is displayed with traditional layout
        $response->assertSee('Business Days Calendar');

        // Check that all day headers are present
        $response->assertSee('Sun');
        $response->assertSee('Mon');
        $response->assertSee('Tue');
        $response->assertSee('Wed');
        $response->assertSee('Thu');
        $response->assertSee('Fri');
        $response->assertSee('Sat');

        // Check that calendar uses 7-column grid
        $response->assertSee('grid grid-cols-7');
    }

    /** @test */
    public function business_days_calendar_shows_only_business_days_and_holidays()
    {
        $year = 2024;
        $month = 1;
        
        // Configure weekends
        WeekendConfiguration::setForMonth($year, $month, [0, 6]); // Sunday and Saturday
        
        // Add a holiday
        HolidayConfiguration::create([
            'name' => 'New Year Day',
            'holiday_date' => Carbon::create($year, $month, 1),
            'year' => $year,
            'month' => $month,
            'type' => 'fixed',
            'is_active' => true
        ]);
        
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.business-days', ['year' => $year, 'month' => $month]));

        $response->assertStatus(200);
        
        // Should show business days
        $response->assertSee('Business Days');
        
        // Should show holidays
        $response->assertSee('Holidays');
        // Note: Holiday indicator might not be visible if no holidays are configured for the test month
        
        // Should not show weekend legend in the calendar legend
        $response->assertDontSee('<span>Weekends</span>', false);
        
        // Should show holiday styling (orange) when holidays are present
        // Note: Orange styling is used for holidays to match the design
    }

    /** @test */
    public function business_days_calendar_uses_flexible_layout_instead_of_grid()
    {
        $year = 2024;
        $month = 1;
        
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.business-days', ['year' => $year, 'month' => $month]));

        $response->assertStatus(200);
        
        // Should use grid layout
        $response->assertSee('grid grid-cols-7 gap-2');
        $response->assertSee('Business Days Calendar');
        
        // Should not have traditional calendar headers
        $response->assertDontSee('<div class="font-semibold text-gray-600 dark:text-gray-400 py-2">Sun</div>', false);
        $response->assertDontSee('<div class="font-semibold text-gray-600 dark:text-gray-400 py-2">Sat</div>', false);
    }

    /** @test */
    public function business_days_calendar_shows_day_names_for_each_date()
    {
        $year = 2024;
        $month = 1;
        
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.business-days', ['year' => $year, 'month' => $month]));

        $response->assertStatus(200);
        
        // Should show traditional calendar headers
        $response->assertSee('Sun');
        $response->assertSee('Mon');
        $response->assertSee('Tue');
        $response->assertSee('Wed');
        $response->assertSee('Thu');
        $response->assertSee('Fri');
        $response->assertSee('Sat');
    }

    /** @test */
    public function business_days_calendar_with_custom_weekend_configuration()
    {
        $year = 2024;
        $month = 1;
        
        // Configure custom weekends (Friday and Saturday)
        WeekendConfiguration::setForMonth($year, $month, [5, 6]); // Friday and Saturday
        
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.business-days', ['year' => $year, 'month' => $month]));

        $response->assertStatus(200);
        
        // Should still not display weekend days regardless of configuration
        $response->assertSee('Business Days Calendar');
        $response->assertDontSee('<div class="text-xs">W</div>', false);
        
        // Should show business days
        $response->assertSee('Business Days Calendar');
    }

    /** @test */
    public function business_days_calendar_handles_month_with_no_business_days()
    {
        $year = 2024;
        $month = 1;
        
        // Configure all days as weekends (extreme case)
        WeekendConfiguration::setForMonth($year, $month, [0, 1, 2, 3, 4, 5, 6]); // All days
        
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.business-days', ['year' => $year, 'month' => $month]));

        $response->assertStatus(200);
        
        // Should still render the page without errors
        $response->assertSee('Business Days Calendar');
        
        // Should still show the calendar structure
        $response->assertSee('Business Days Calendar');
    }

    /** @test */
    public function non_admin_cannot_access_business_days_calendar()
    {
        $regularUser = User::factory()->create([
            'role' => 'user',
            'mode' => 'regular',
            'status' => 'active'
        ]);

        $response = $this->actingAs($regularUser)
            ->get(route('admin.business-days'));

        $response->assertStatus(403);
    }

    /** @test */
    public function business_days_calendar_shows_current_month_by_default()
    {
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.business-days'));

        $response->assertStatus(200);
        
        $currentMonth = Carbon::now()->format('F Y');
        $response->assertSee($currentMonth);
    }

    /** @test */
    public function business_days_calendar_accepts_year_and_month_parameters()
    {
        $year = 2023;
        $month = 6; // June

        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.business-days', ['year' => $year, 'month' => $month]));

        $response->assertStatus(200);

        $expectedMonth = Carbon::create($year, $month, 1)->format('F Y');
        $response->assertSee($expectedMonth);
    }

    /** @test */
    public function business_days_calendar_displays_holidays_with_correct_styling()
    {
        $year = 2024;
        $month = 1;

        // Configure weekends
        WeekendConfiguration::setForMonth($year, $month, [0, 6]); // Sunday and Saturday

        // Add a holiday
        HolidayConfiguration::create([
            'name' => 'New Year Day',
            'holiday_date' => Carbon::create($year, $month, 1),
            'year' => $year,
            'month' => $month,
            'type' => 'fixed',
            'is_active' => true
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.business-days', ['year' => $year, 'month' => $month]));

        $response->assertStatus(200);

        // Should show holidays with orange styling (matching the image design)
        $response->assertSee('bg-orange-100 dark:bg-orange-900/30');
        $response->assertSee('Holidays');
    }
}
