<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\LunchOrder;
use App\Models\LunchPrice;
use App\Models\Bill;
use App\Services\LunchOrderService;
use App\Services\BillingService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class LunchManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $lunchOrderService;
    protected $billingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->lunchOrderService = app(LunchOrderService::class);
        $this->billingService = app(BillingService::class);
    }

    public function test_user_can_access_lunch_dashboard()
    {
        $user = User::factory()->create(['role' => 'user', 'mode' => 'regular']);
        
        $response = $this->actingAs($user)->get('/lunch');
        
        $response->assertStatus(200);
        $response->assertSee('Lunch Management');
    }

    public function test_admin_can_access_admin_dashboard()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $response = $this->actingAs($admin)->get('/admin');
        
        $response->assertStatus(200);
        $response->assertSee('Admin Dashboard');
    }

    public function test_regular_user_gets_auto_order()
    {
        $regularUser = User::factory()->create(['role' => 'user', 'mode' => 'regular']);
        $guestUser = User::factory()->create(['role' => 'user', 'mode' => 'guest']);
        
        $ordersCreated = $this->lunchOrderService->createAutoOrdersForDate();
        
        $this->assertEquals(1, $ordersCreated);
        $this->assertTrue($this->lunchOrderService->hasUserOrderedForDate($regularUser));
        $this->assertFalse($this->lunchOrderService->hasUserOrderedForDate($guestUser));
    }

    public function test_user_can_cancel_order()
    {
        $user = User::factory()->create(['role' => 'user', 'mode' => 'regular']);
        
        // Create an order first
        $this->lunchOrderService->placeOrder($user);
        $this->assertTrue($this->lunchOrderService->hasUserOrderedForDate($user));
        
        // Cancel the order
        $cancelled = $this->lunchOrderService->cancelOrder($user);
        $this->assertTrue($cancelled);
        $this->assertFalse($this->lunchOrderService->hasUserOrderedForDate($user));
    }

    public function test_guest_user_can_place_order()
    {
        $user = User::factory()->create(['role' => 'user', 'mode' => 'guest']);
        
        $order = $this->lunchOrderService->placeOrder($user);
        
        $this->assertNotNull($order);
        $this->assertTrue($this->lunchOrderService->hasUserOrderedForDate($user));
    }

    public function test_lunch_price_management()
    {
        $year = 2024;
        $month = 6;
        $price = 150.00;
        
        // Set price
        $lunchPrice = $this->billingService->setLunchPrice($year, $month, $price);
        $this->assertNotNull($lunchPrice);
        
        // Get price
        $retrievedPrice = $this->billingService->getLunchPrice($year, $month);
        $this->assertEquals($price, $retrievedPrice);
    }

    public function test_bill_generation()
    {
        $user = User::factory()->create(['role' => 'user', 'mode' => 'regular']);
        $year = Carbon::now()->year;
        $month = Carbon::now()->month;
        
        // Set lunch price
        $this->billingService->setLunchPrice($year, $month, 100.00);
        
        // Create some lunch orders
        for ($i = 1; $i <= 5; $i++) {
            LunchOrder::create([
                'user_id' => $user->id,
                'order_date' => Carbon::create($year, $month, $i),
                'status' => 'ordered',
            ]);
        }
        
        // Generate bill
        $bill = $this->billingService->generateBillForUser($user, $year, $month);
        
        $this->assertNotNull($bill);
        $this->assertEquals(5, $bill->lunch_count);
        $this->assertEquals(100.00, $bill->rate);
        $this->assertEquals(500.00, $bill->amount);
    }

    public function test_admin_middleware_blocks_non_admin()
    {
        $user = User::factory()->create(['role' => 'user']);
        
        $response = $this->actingAs($user)->get('/admin');
        
        $response->assertStatus(403);
    }

    public function test_place_order_api_endpoint()
    {
        $user = User::factory()->create(['role' => 'user', 'mode' => 'guest']);
        
        $response = $this->actingAs($user)->postJson('/lunch/place-order');
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        $this->assertTrue($this->lunchOrderService->hasUserOrderedForDate($user));
    }

    public function test_cancel_order_api_endpoint()
    {
        $user = User::factory()->create(['role' => 'user', 'mode' => 'regular']);
        
        // Create an order first
        $this->lunchOrderService->placeOrder($user);
        
        $response = $this->actingAs($user)->postJson('/lunch/cancel-order');
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        $this->assertFalse($this->lunchOrderService->hasUserOrderedForDate($user));
    }

    public function test_monthly_summary_calculation()
    {
        $user1 = User::factory()->create(['role' => 'user']);
        $user2 = User::factory()->create(['role' => 'user']);
        $year = Carbon::now()->year;
        $month = Carbon::now()->month;
        
        // Set lunch price
        $this->billingService->setLunchPrice($year, $month, 120.00);
        
        // Create orders for both users
        LunchOrder::create(['user_id' => $user1->id, 'order_date' => Carbon::create($year, $month, 1), 'status' => 'ordered']);
        LunchOrder::create(['user_id' => $user1->id, 'order_date' => Carbon::create($year, $month, 2), 'status' => 'ordered']);
        LunchOrder::create(['user_id' => $user2->id, 'order_date' => Carbon::create($year, $month, 1), 'status' => 'ordered']);
        
        // Generate bills
        $this->billingService->generateBillsForMonth($year, $month);
        
        // Get summary
        $summary = $this->billingService->getMonthlySummary($year, $month);
        
        $this->assertEquals(2, $summary['total_users']);
        $this->assertEquals(3, $summary['total_lunches']);
        $this->assertEquals(360.00, $summary['total_amount']); // 3 lunches * 120
        $this->assertEquals(120.00, $summary['lunch_price']);
    }
}
