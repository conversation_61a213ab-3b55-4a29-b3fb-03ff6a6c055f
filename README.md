# LMS - Lunch Management System

## Setup Instructions

### Prerequisites
- <PERSON><PERSON> and Docker Compose
- Git

### Installation

1. **Setup environment file**
   ```bash
   cp .env.example .env
   ```
   Update the necessary data in the `.env` file (database credentials, app settings, etc.)

2. **Build and start Docker containers**
   ```bash
   docker-compose --env-file .env up -d --build
   ```

3. **Access the application container**
   ```bash
   docker-compose exec app sh
   ```

4. **Install PHP dependencies**

   **For Production:**
   ```bash
   composer install --no-dev --no-interaction --optimize-autoloader --no-scripts --no-cache
   ```

   **For Development:**
   ```bash
   composer install --no-interaction --optimize-autoloader --no-scripts --no-cache
   ```

5. **Generate application key**
   ```bash
   php artisan key:generate
   ```

6. **Install and build frontend assets**
   ```bash
   npm install
   npm run build
   ```

7. **Run database migrations**
   ```bash
   php artisan migrate
   ```

8. **Clear and cache configuration**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

### Configuration

After installation, make sure to:
- Configure your database settings in the `.env` file
- Set up any additional environment variables as needed
- Verify all services are running properly
