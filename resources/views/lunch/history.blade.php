<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 dark:text-gray-200 leading-tight">
            {{ __('Lunch History') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Navigation -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <a href="{{ route('lunch.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Month/Year Filter -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Filter by Month</h3>
                    <form method="GET" action="{{ route('lunch.history') }}" class="flex space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                            <select name="year" class="mt-1 block border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @for($y = 2020; $y <= 2030; $y++)
                                    <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                @endfor
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Month</label>
                            <select name="month" class="mt-1 block border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @for($m = 1; $m <= 12; $m++)
                                    <option value="{{ $m }}" {{ $month == $m ? 'selected' : '' }}>
                                        {{ DateTime::createFromFormat('!m', $m)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                </svg>
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Monthly Bill Summary -->
            @if($bill)
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">
                        {{ DateTime::createFromFormat('!m', $month)->format('F') }} {{ $year }} Bill Summary
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded">
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $bill->lunch_count }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Lunches Taken</div>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900 p-4 rounded">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400">৳{{ number_format($bill->rate, 2) }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Rate per Lunch</div>
                        </div>
                        <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded">
                            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">৳{{ number_format($bill->amount, 2) }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Total Amount</div>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded">
                            <div class="text-2xl font-bold text-gray-600 dark:text-gray-400">{{ $orders->where('status', 'cancelled')->count() }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Cancelled Orders</div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Orders History -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">
                        Lunch Orders for {{ DateTime::createFromFormat('!m', $month)->format('F') }} {{ $year }}
                    </h3>
                    
                    @if($orders->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700">
                                        <th class="px-4 py-2 text-left">Date</th>
                                        <th class="px-4 py-2 text-left">Day</th>
                                        <th class="px-4 py-2 text-left">Status</th>
                                        <th class="px-4 py-2 text-left">Order Time</th>
                                        <th class="px-4 py-2 text-left">Last Updated</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($orders as $order)
                                    <tr class="border-b">
                                        <td class="px-4 py-2">{{ $order->order_date->format('Y-m-d') }}</td>
                                        <td class="px-4 py-2">{{ $order->order_date->format('l') }}</td>
                                        <td class="px-4 py-2">
                                            @if($order->status === 'ordered')
                                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">
                                                    ✓ Ordered
                                                </span>
                                            @else
                                                <span class="px-2 py-1 bg-red-100 text-red-800 rounded text-sm">
                                                    ✗ Cancelled
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-2">{{ $order->created_at->format('H:i') }}</td>
                                        <td class="px-4 py-2">{{ $order->updated_at->format('H:i') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Summary Stats -->
                        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-green-50 p-4 rounded">
                                <div class="text-lg font-bold text-green-600">{{ $orders->where('status', 'ordered')->count() }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Total Ordered</div>
                            </div>
                            <div class="bg-red-50 p-4 rounded">
                                <div class="text-lg font-bold text-red-600">{{ $orders->where('status', 'cancelled')->count() }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Total Cancelled</div>
                            </div>
                            <div class="bg-blue-50 p-4 rounded">
                                <div class="text-lg font-bold text-blue-600">{{ $orders->count() }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Total Records</div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="text-gray-500 dark:text-gray-400 text-lg">No lunch orders found for this month.</div>
                            <p class="text-gray-400 mt-2">
                                @if(auth()->user()->isGuest())
                                    As a guest user, you need to manually place orders.
                                @else
                                    Auto-orders will be created daily for regular users.
                                @endif
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
