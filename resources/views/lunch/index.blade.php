<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 dark:text-gray-200 leading-tight">
            {{ __('Lunch Management') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- User Info Card -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg sm:rounded-xl mb-6 border border-gray-200 dark:border-gray-700">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                <span class="text-white font-bold text-lg">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Welcome back, {{ $user->name }}!</h3>
                            <div class="flex items-center space-x-4 mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $user->mode === 'regular' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' }}">
                                    {{ ucfirst($user->mode) }} User
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ ($user->status ?? 'active') === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                    {{ ucfirst($user->status ?? 'active') }}
                                </span>
                                @if($user->isAdmin())
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd" />
                                        </svg>
                                        Administrator
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Lunch Status -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg sm:rounded-xl mb-6 border border-gray-200 dark:border-gray-700">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h3 class="text-lg font-semibold">Today's Lunch Status</h3>
                        </div>
                        @if(isset($orderingStatus['deadline_time']))
                            <div class="text-right">
                                <div class="text-sm text-gray-600 dark:text-gray-400">Ordering Deadline</div>
                                <div class="text-lg font-semibold {{ $orderingStatus['is_past_deadline'] ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400' }}">
                                    {{ $orderingStatus['deadline_time'] }}
                                </div>
                                @if(isset($orderingStatus['current_time']))
                                    <div class="text-xs text-gray-500 dark:text-gray-500">
                                        Current: {{ $orderingStatus['current_time'] }}
                                    </div>
                                @endif
                                @if($orderingStatus['can_order'] && !$orderingStatus['is_past_deadline'])
                                    <div class="text-xs text-blue-600 dark:text-blue-400 mt-1" id="timeRemaining">
                                        ⏱️ Time remaining: <span id="countdown"></span>
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>

                    @if(!$user->isActive())
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                                <div>
                                    <h4 class="font-semibold text-red-800 dark:text-red-300">Account Inactive</h4>
                                    <p class="text-red-700 dark:text-red-400 text-sm">Your account is inactive. You cannot place or modify lunch orders.</p>
                                </div>
                            </div>
                        </div>
                    @elseif(!$orderingStatus['can_order'])
                        @if(isset($orderingStatus['is_past_deadline']) && $orderingStatus['is_past_deadline'])
                            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <div>
                                        <h4 class="font-semibold text-red-800 dark:text-red-300">⏰ Ordering Deadline Passed</h4>
                                        <p class="text-red-700 dark:text-red-400 text-sm">{{ $orderingStatus['message'] }}</p>
                                        <p class="text-red-600 dark:text-red-500 text-xs mt-1">
                                            Deadline was at {{ $orderingStatus['deadline_time'] }} (Current time: {{ $orderingStatus['current_time'] }})
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-yellow-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                    <div>
                                        <h4 class="font-semibold text-yellow-800 dark:text-yellow-300">
                                            @if($orderingStatus['is_weekend'])
                                                Weekend Day
                                            @elseif($orderingStatus['is_holiday'])
                                                Holiday
                                            @else
                                                Non-Business Day
                                            @endif
                                        </h4>
                                        <p class="text-yellow-700 dark:text-yellow-400 text-sm">{{ $orderingStatus['message'] }}</p>
                                        @if(isset($orderingStatus['next_business_day']))
                                            <p class="text-yellow-600 dark:text-yellow-500 text-xs mt-1">
                                                Next business day: {{ $orderingStatus['next_business_day']->format('l, F j, Y') }}
                                            </p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif
                    @elseif($todayOrder)
                        @if($todayOrder->isOrdered())
                            <div class="bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded mb-4">
                                <strong>✓ Lunch Ordered</strong> - You have lunch ordered for today.
                            </div>
                            @if($orderingStatus['can_order'])
                                <button onclick="cancelOrder()" class="inline-flex items-center px-3 py-1.5 border border-red-500 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    Cancel Today's Lunch
                                </button>
                            @else
                                <button disabled class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-400 text-sm font-medium rounded cursor-not-allowed opacity-50">
                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    Cancel Today's Lunch
                                </button>
                            @endif
                        @else
                            <div class="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4">
                                <strong>✗ Lunch Cancelled</strong> - You have cancelled lunch for today.
                            </div>
                            @if($user->isGuest())
                                @if($orderingStatus['can_order'])
                                    <button onclick="placeOrder()" class="inline-flex items-center px-3 py-1.5 border border-green-500 text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        Order Lunch for Today
                                    </button>
                                @else
                                    <button disabled class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-400 text-sm font-medium rounded cursor-not-allowed opacity-50">
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        Order Lunch for Today
                                    </button>
                                @endif
                            @endif
                        @endif
                    @else
                        @if($user->isRegular())
                            <div class="bg-yellow-100 dark:bg-yellow-900 border border-yellow-400 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300 px-4 py-3 rounded mb-4">
                                <strong>⚠ No Auto Order</strong> - Auto order hasn't been created yet for today.
                            </div>
                        @else
                            <div class="bg-gray-100 dark:bg-gray-700 border border-gray-400 dark:border-gray-600 text-gray-700 dark:text-gray-300 px-4 py-3 rounded mb-4">
                                <strong>No Order</strong> - You haven't ordered lunch for today.
                            </div>
                            @if($orderingStatus['can_order'])
                                <button onclick="placeOrder()" class="inline-flex items-center px-3 py-1.5 border border-green-500 text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Order Lunch for Today
                                </button>
                            @else
                                <button disabled class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-400 text-sm font-medium rounded cursor-not-allowed opacity-50">
                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Order Lunch for Today
                                </button>
                            @endif
                        @endif
                    @endif
                </div>
            </div>

            <!-- Current Month Bill -->
            @if($currentBill)
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg sm:rounded-xl mb-6 border border-gray-200 dark:border-gray-700">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex items-center mb-6">
                        <svg class="w-6 h-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        <h3 class="text-lg font-semibold">Current Month Bill</h3>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $currentBill->lunch_count }}</div>
                                    <div class="text-sm font-medium text-blue-700 dark:text-blue-300 mt-1">Lunches Taken</div>
                                </div>
                                <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                            </div>
                        </div>
                        <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-xl border border-green-200 dark:border-green-800">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-3xl font-bold text-green-600 dark:text-green-400">${{ number_format($currentBill->rate, 2) }}</div>
                                    <div class="text-sm font-medium text-green-700 dark:text-green-300 mt-1">Rate per Lunch</div>
                                </div>
                                <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                            </div>
                        </div>
                        <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">৳{{ number_format($currentBill->amount, 2) }}</div>
                                    <div class="text-sm font-medium text-purple-700 dark:text-purple-300 mt-1">Total Amount</div>
                                </div>
                                <svg class="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Tomorrow's Lunch Status -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg sm:rounded-xl mb-6 border border-gray-200 dark:border-gray-700">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <h3 class="text-lg font-semibold">Tomorrow's Lunch Status</h3>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600 dark:text-gray-400">{{ $tomorrow->format('l') }}</div>
                            <div class="text-lg font-semibold text-purple-600 dark:text-purple-400">
                                {{ $tomorrow->format('F j, Y') }}
                            </div>
                            @if($canCancelTomorrow && \Carbon\Carbon::now()->hour >= 14)
                                <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                                    ✓ Can cancel after 2:00 PM
                                </div>
                            @elseif($hasTomorrowOrder && \Carbon\Carbon::now()->hour < 14)
                                <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">
                                    ⏱️ Can cancel after 2:00 PM
                                </div>
                            @endif
                        </div>
                    </div>

                    @if($tomorrowOrderingStatus['is_business_day'])
                        @if($hasTomorrowOrder)
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <svg class="w-8 h-8 text-green-600 dark:text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-semibold text-green-800 dark:text-green-300">✓ Lunch Ordered</h4>
                                            <p class="text-green-700 dark:text-green-400 text-sm">You have ordered lunch for tomorrow.</p>
                                        </div>
                                    </div>
                                    @if($canCancelTomorrow)
                                        <button onclick="cancelTomorrowOrder()" class="inline-flex items-center px-3 py-1.5 border border-red-500 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 text-sm font-medium rounded transition-colors duration-200">
                                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                            Cancel
                                        </button>
                                    @else
                                        <span class="text-sm text-gray-500 dark:text-gray-400 italic">
                                            {{ $tomorrowCancelReason }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @else
                            @if($tomorrowOrderingStatus['can_order'])
                                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <div>
                                                <h4 class="font-semibold text-blue-800 dark:text-blue-300">No Order Yet</h4>
                                                <p class="text-blue-700 dark:text-blue-400 text-sm">{{ $tomorrowOrderingStatus['message'] }}</p>
                                            </div>
                                        </div>
                                        <button onclick="placeTomorrowOrder()" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                            Order Now
                                        </button>
                                    </div>
                                </div>
                            @else
                                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4">
                                    <div class="flex items-center">
                                        <svg class="w-8 h-8 text-yellow-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-semibold text-yellow-800 dark:text-yellow-300">Cannot Order</h4>
                                            <p class="text-yellow-700 dark:text-yellow-400 text-sm">{{ $tomorrowOrderingStatus['message'] }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endif
                    @else
                        <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4">
                            <div class="flex items-center">
                                <svg class="w-8 h-8 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <div>
                                    <h4 class="font-semibold text-gray-800 dark:text-gray-300">Non-Business Day</h4>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm">Tomorrow is not a business day.</p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Navigation -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg sm:rounded-xl mb-6 border border-gray-200 dark:border-gray-700">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex items-center mb-4">
                        <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <h3 class="text-lg font-semibold">Quick Actions</h3>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <a href="{{ route('lunch.upcoming') }}" class="flex items-center p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-200 relative">
                            <svg class="w-8 h-8 text-green-600 dark:text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <div class="font-semibold text-green-800 dark:text-green-300">Upcoming Orders</div>
                                    @if($upcomingSummary['total'] > 0)
                                        <span class="px-2 py-1 bg-green-600 text-white text-xs font-bold rounded-full">{{ $upcomingSummary['total'] }}</span>
                                    @endif
                                </div>
                                <div class="text-sm text-green-600 dark:text-green-400">
                                    @if($upcomingSummary['total'] > 0)
                                        {{ $upcomingSummary['ordered'] }} ordered, {{ $upcomingSummary['cancellable'] }} cancellable
                                        @if($upcomingSummary['next_order_date'])
                                            <br><span class="text-xs">Next: {{ $upcomingSummary['next_order_date']->format('M j') }}</span>
                                        @endif
                                    @else
                                        Manage future lunch orders
                                    @endif
                                </div>
                            </div>
                            @if($upcomingSummary['cancellable'] > 0)
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">!</span>
                                </div>
                            @endif
                        </a>

                        <a href="{{ route('lunch.history') }}" class="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200">
                            <svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <div class="font-semibold text-blue-800 dark:text-blue-300">Order History</div>
                                <div class="text-sm text-blue-600 dark:text-blue-400">View past orders & bills</div>
                            </div>
                        </a>

                        <button onclick="loadTodaySummary()" class="flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors duration-200">
                            <svg class="w-8 h-8 text-purple-600 dark:text-purple-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            <div>
                                <div class="font-semibold text-purple-800 dark:text-purple-300">Today's Summary</div>
                                <div class="text-sm text-purple-600 dark:text-purple-400">View today's order stats</div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script>
        // Countdown timer for ordering deadline
        @if($orderingStatus['can_order'] && !($orderingStatus['is_past_deadline'] ?? false))
        function updateCountdown() {
            const now = new Date();
            const deadline = new Date();
            deadline.setHours(10, 0, 0, 0); // 10:00 AM today

            if (now >= deadline) {
                document.getElementById('timeRemaining').style.display = 'none';
                location.reload(); // Refresh to update the status
                return;
            }

            const diff = deadline - now;
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);

            const countdownElement = document.getElementById('countdown');
            if (countdownElement) {
                if (hours > 0) {
                    countdownElement.textContent = `${hours}h ${minutes}m ${seconds}s`;
                } else {
                    countdownElement.textContent = `${minutes}m ${seconds}s`;
                }
            }
        }

        // Update countdown every second
        updateCountdown();
        setInterval(updateCountdown, 1000);
        @endif

        function placeOrder() {
            fetch('{{ route("lunch.place-order") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while placing the order.');
            });
        }

        function cancelOrder() {
            if (confirm('Are you sure you want to cancel today\'s lunch?')) {
                fetch('{{ route("lunch.cancel-order") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while cancelling the order.');
                });
            }
        }

        function loadTodaySummary() {
            fetch('{{ route("lunch.today-summary") }}')
            .then(response => response.json())
            .then(data => {
                alert(`Today's Summary:\nOrdered: ${data.total_ordered}\nCancelled: ${data.total_cancelled}`);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while loading the summary.');
            });
        }

        function placeTomorrowOrder() {
            if (confirm('Are you sure you want to place a lunch order for tomorrow?')) {
                fetch('{{ route("lunch.place-tomorrow-order") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while placing tomorrow\'s order.');
                });
            }
        }

        function cancelTomorrowOrder() {
            if (confirm('Are you sure you want to cancel tomorrow\'s lunch order?')) {
                fetch('{{ route("lunch.cancel-tomorrow-order") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while cancelling tomorrow\'s order.');
                });
            }
        }
    </script>
</x-app-layout>
