<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Weekend & Holiday Configuration') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Navigation -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <a href="{{ route('admin.business-days') }}" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200">
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Back to Business Days
                    </a>
                </div>
            </div>

            <!-- Month Filter -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Month Selection</h3>
                    
                    <form method="GET" action="{{ route('admin.weekend-holiday-config') }}" class="flex space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                            <select name="year" class="mt-1 block border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @for($y = 2020; $y <= 2030; $y++)
                                    <option value="{{ $y }}" {{ $y == $monthSummary['year'] ? 'selected' : '' }}>{{ $y }}</option>
                                @endfor
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Month</label>
                            <select name="month" class="mt-1 block border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @for($m = 1; $m <= 12; $m++)
                                    <option value="{{ $m }}" {{ $m == $monthSummary['month'] ? 'selected' : '' }}>
                                        {{ DateTime::createFromFormat('!m', $m)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                </svg>
                                View Month
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Weekend Configuration -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium">Weekend Configuration for {{ $monthSummary['month_name'] }}</h3>
                        <div class="flex space-x-2">
                            <button onclick="showWeekendTemplatesForm()" class="inline-flex items-center px-3 py-1.5 border border-purple-500 text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                                </svg>
                                Templates
                            </button>
                            <button onclick="showBulkConfigForm()" class="inline-flex items-center px-3 py-1.5 border border-indigo-500 text-indigo-500 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z" />
                                </svg>
                                Bulk Configure
                            </button>
                            <button onclick="showWeekendConfigForm()" class="inline-flex items-center px-3 py-1.5 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                Configure This Month
                            </button>
                        </div>
                    </div>

                    @if($weekendConfig)
                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-300 mb-2">Current Weekend Configuration</h4>
                            <p class="text-blue-700 dark:text-blue-400 text-sm mb-2">
                                <strong>Weekend Days:</strong> {{ implode(', ', $weekendConfig->weekend_day_names) }}
                            </p>
                            @if($weekendConfig->description)
                                <p class="text-blue-600 dark:text-blue-500 text-xs mb-2">{{ $weekendConfig->description }}</p>
                            @endif
                            <div class="flex items-center text-xs text-blue-600 dark:text-blue-400">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Last updated: {{ $weekendConfig->updated_at->format('M j, Y g:i A') }}
                            </div>
                        </div>
                    @else
                        <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4">
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                No custom weekend configuration set for this month. Using default weekends (Saturday & Sunday).
                            </p>
                        </div>
                    @endif

                    <!-- Weekend Preview -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/10 dark:to-indigo-900/10 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-800 dark:text-blue-300 mb-3">Weekend Preview for {{ $monthSummary['month_name'] }}</h4>
                        <div class="grid grid-cols-7 gap-1 text-center text-xs">
                            @php
                                $dayLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                                $currentWeekendDays = $weekendConfig ? $weekendConfig->weekend_days : [0, 6];
                            @endphp
                            @foreach($dayLabels as $index => $label)
                                <div class="p-2 rounded {{ in_array($index, $currentWeekendDays) ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 font-semibold' : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' }}">
                                    {{ $label }}
                                    @if(in_array($index, $currentWeekendDays))
                                        <div class="text-xs">Weekend</div>
                                    @else
                                        <div class="text-xs">Work Day</div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                        <div class="mt-3 flex justify-between text-xs text-blue-600 dark:text-blue-400">
                            <span>🟢 Work Days: {{ 7 - count($currentWeekendDays) }} days</span>
                            <span>🟠 Weekend Days: {{ count($currentWeekendDays) }} days</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Holiday Configuration -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium">Holiday Configuration for {{ $monthSummary['month_name'] }}</h3>
                        <div class="flex space-x-2">
                            <button onclick="showHolidayTemplatesForm()" class="inline-flex items-center px-3 py-1.5 border border-purple-500 text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                                </svg>
                                Holiday Templates
                            </button>
                            <button onclick="showBulkHolidayForm()" class="inline-flex items-center px-3 py-1.5 border border-indigo-500 text-indigo-500 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z" />
                                </svg>
                                Bulk Import
                            </button>
                            <button onclick="showAddHolidayForm()" class="inline-flex items-center px-3 py-1.5 border border-green-500 text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Add Holiday
                            </button>
                        </div>
                    </div>

                    @if($holidayConfigs->count() > 0)
                        <div class="space-y-3">
                            @foreach($holidayConfigs as $holiday)
                                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex justify-between items-center">
                                    <div>
                                        <h4 class="font-semibold text-red-800 dark:text-red-300">{{ $holiday->name }}</h4>
                                        <p class="text-red-700 dark:text-red-400 text-sm">
                                            {{ $holiday->formatted_date }} ({{ $holiday->day_of_week }})
                                        </p>
                                        @if($holiday->description)
                                            <p class="text-red-600 dark:text-red-500 text-xs mt-1">{{ $holiday->description }}</p>
                                        @endif
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/40 text-red-800 dark:text-red-300 mt-2">
                                            {{ ucfirst($holiday->type) }}
                                        </span>
                                    </div>
                                    <button onclick="removeHoliday({{ $holiday->id }})" class="inline-flex items-center px-2 py-1 border border-red-500 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 text-xs font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-red-500">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        Remove
                                    </button>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                No holidays configured for this month.
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Weekend Configuration Modal -->
    <div id="weekendConfigModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Configure Weekend Days</h3>
                <form id="weekendConfigForm">
                    <input type="hidden" name="year" value="{{ $monthSummary['year'] }}">
                    <input type="hidden" name="month" value="{{ $monthSummary['month'] }}">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Weekend Days</label>
                        <div class="space-y-2">
                            @php
                                $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                                $currentWeekendDays = $weekendConfig ? $weekendConfig->weekend_days : [0, 6];
                            @endphp
                            @foreach($days as $index => $day)
                                <label class="flex items-center">
                                    <input type="checkbox" name="weekend_days[]" value="{{ $index }}" 
                                           {{ in_array($index, $currentWeekendDays) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $day }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description (Optional)</label>
                        <textarea name="description" rows="2" 
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  placeholder="e.g., Special weekend configuration for holidays">{{ $weekendConfig ? $weekendConfig->description : '' }}</textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="hideWeekendConfigForm()" 
                                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Save Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Holiday Modal -->
    <div id="addHolidayModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Add Holiday</h3>
                <form id="addHolidayForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Holiday Date</label>
                        <input type="date" name="holiday_date" required 
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Holiday Name</label>
                        <input type="text" name="name" required 
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               placeholder="e.g., Independence Day">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Type</label>
                        <select name="type" required class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="custom">Custom</option>
                            <option value="fixed">Fixed Annual</option>
                            <option value="variable">Variable</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description (Optional)</label>
                        <textarea name="description" rows="2" 
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  placeholder="Additional details about this holiday"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="hideAddHolidayForm()" 
                                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-green-500 text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Add Holiday
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Weekend Templates Modal -->
    <div id="weekendTemplatesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Weekend Templates</h3>
                <div class="space-y-3">
                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" onclick="applyTemplate([0, 6], 'Standard Weekend (Saturday & Sunday)')">
                        <h4 class="font-semibold text-sm">Standard Weekend</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Saturday & Sunday</p>
                        <div class="flex space-x-1 mt-2">
                            @foreach(['S', 'M', 'T', 'W', 'T', 'F', 'S'] as $index => $day)
                                <div class="w-6 h-6 text-xs flex items-center justify-center rounded {{ in_array($index, [0, 6]) ? 'bg-orange-200 text-orange-800' : 'bg-green-200 text-green-800' }}">{{ $day }}</div>
                            @endforeach
                        </div>
                    </div>

                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" onclick="applyTemplate([5, 6], 'Western Weekend (Friday & Saturday)')">
                        <h4 class="font-semibold text-sm">Western Weekend</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Friday & Saturday</p>
                        <div class="flex space-x-1 mt-2">
                            @foreach(['S', 'M', 'T', 'W', 'T', 'F', 'S'] as $index => $day)
                                <div class="w-6 h-6 text-xs flex items-center justify-center rounded {{ in_array($index, [5, 6]) ? 'bg-orange-200 text-orange-800' : 'bg-green-200 text-green-800' }}">{{ $day }}</div>
                            @endforeach
                        </div>
                    </div>

                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" onclick="applyTemplate([0, 1], 'Alternative Weekend (Sunday & Monday)')">
                        <h4 class="font-semibold text-sm">Alternative Weekend</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Sunday & Monday</p>
                        <div class="flex space-x-1 mt-2">
                            @foreach(['S', 'M', 'T', 'W', 'T', 'F', 'S'] as $index => $day)
                                <div class="w-6 h-6 text-xs flex items-center justify-center rounded {{ in_array($index, [0, 1]) ? 'bg-orange-200 text-orange-800' : 'bg-green-200 text-green-800' }}">{{ $day }}</div>
                            @endforeach
                        </div>
                    </div>

                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" onclick="applyTemplate([6], 'Single Weekend (Saturday Only)')">
                        <h4 class="font-semibold text-sm">Single Weekend</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Saturday Only</p>
                        <div class="flex space-x-1 mt-2">
                            @foreach(['S', 'M', 'T', 'W', 'T', 'F', 'S'] as $index => $day)
                                <div class="w-6 h-6 text-xs flex items-center justify-center rounded {{ in_array($index, [6]) ? 'bg-orange-200 text-orange-800' : 'bg-green-200 text-green-800' }}">{{ $day }}</div>
                            @endforeach
                        </div>
                    </div>

                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" onclick="applyTemplate([1, 2, 3, 4, 5], 'Reverse Schedule (Monday-Friday Weekend)')">
                        <h4 class="font-semibold text-sm">Reverse Schedule</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Monday-Friday Weekend</p>
                        <div class="flex space-x-1 mt-2">
                            @foreach(['S', 'M', 'T', 'W', 'T', 'F', 'S'] as $index => $day)
                                <div class="w-6 h-6 text-xs flex items-center justify-center rounded {{ in_array($index, [1, 2, 3, 4, 5]) ? 'bg-orange-200 text-orange-800' : 'bg-green-200 text-green-800' }}">{{ $day }}</div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-2 mt-4">
                    <button type="button" onclick="hideWeekendTemplatesForm()"
                            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Configuration Modal -->
    <div id="bulkConfigModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Bulk Weekend Configuration</h3>
                <form id="bulkConfigForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Apply To</label>
                        <select name="apply_to" required class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="current_year">All months in {{ $monthSummary['year'] }}</option>
                            <option value="next_year">All months in {{ $monthSummary['year'] + 1 }}</option>
                            <option value="both_years">Both {{ $monthSummary['year'] }} and {{ $monthSummary['year'] + 1 }}</option>
                            <option value="custom_range">Custom date range</option>
                        </select>
                    </div>

                    <div id="customRangeFields" class="mb-4 hidden">
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">From</label>
                                <input type="month" name="start_month" class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">To</label>
                                <input type="month" name="end_month" class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Weekend Days</label>
                        <div class="space-y-2">
                            @foreach(['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'] as $index => $day)
                                <label class="flex items-center">
                                    <input type="checkbox" name="bulk_weekend_days[]" value="{{ $index }}"
                                           {{ in_array($index, [0, 6]) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $day }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                        <textarea name="bulk_description" rows="2"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  placeholder="Description for bulk configuration"></textarea>
                    </div>

                    <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded p-3">
                        <p class="text-sm text-yellow-800 dark:text-yellow-300">
                            <strong>Warning:</strong> This will overwrite existing weekend configurations for the selected period.
                        </p>
                    </div>

                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="hideBulkConfigForm()"
                                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-indigo-500 text-indigo-500 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Apply Bulk Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Holiday Templates Modal -->
    <div id="holidayTemplatesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Holiday Templates</h3>
                <div class="space-y-3">
                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" onclick="applyHolidayTemplate('us_federal')">
                        <h4 class="font-semibold text-sm">US Federal Holidays</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">New Year's Day, Independence Day, Thanksgiving, Christmas</p>
                    </div>

                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" onclick="applyHolidayTemplate('religious')">
                        <h4 class="font-semibold text-sm">Religious Holidays</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Christmas, Easter, Good Friday</p>
                    </div>

                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" onclick="applyHolidayTemplate('international')">
                        <h4 class="font-semibold text-sm">International Holidays</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">New Year's Day, Labor Day, Christmas</p>
                    </div>

                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" onclick="applyHolidayTemplate('company')">
                        <h4 class="font-semibold text-sm">Company Holidays</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Founder's Day, Company Anniversary, Team Building Days</p>
                    </div>
                </div>

                <div class="flex justify-end space-x-2 mt-4">
                    <button type="button" onclick="hideHolidayTemplatesForm()"
                            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Holiday Import Modal -->
    <div id="bulkHolidayModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Bulk Holiday Import</h3>
                <form id="bulkHolidayForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Import Method</label>
                        <select name="import_method" required class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="csv">CSV Upload</option>
                            <option value="manual">Manual Entry</option>
                            <option value="year_template">Apply to Entire Year</option>
                        </select>
                    </div>

                    <div id="csvUploadSection" class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">CSV File</label>
                        <input type="file" name="csv_file" accept=".csv"
                               class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <p class="text-xs text-gray-500 mt-1">Format: date,name,description,type</p>
                    </div>

                    <div id="manualEntrySection" class="mb-4 hidden">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Holiday List</label>
                        <textarea name="holiday_list" rows="4"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  placeholder="2024-01-01,New Year's Day,First day of the year,fixed&#10;2024-07-04,Independence Day,National holiday,fixed"></textarea>
                        <p class="text-xs text-gray-500 mt-1">One holiday per line: date,name,description,type</p>
                    </div>

                    <div id="yearTemplateSection" class="mb-4 hidden">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Year</label>
                        <select name="target_year" class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @for($y = 2020; $y <= 2030; $y++)
                                <option value="{{ $y }}" {{ $y == $monthSummary['year'] ? 'selected' : '' }}>{{ $y }}</option>
                            @endfor
                        </select>
                        <p class="text-xs text-gray-500 mt-1">Apply current month's holidays to all months in the selected year</p>
                    </div>

                    <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded p-3">
                        <p class="text-sm text-yellow-800 dark:text-yellow-300">
                            <strong>Note:</strong> Bulk import will add holidays to existing configurations. Duplicate dates will be updated.
                        </p>
                    </div>

                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="hideBulkHolidayForm()"
                                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-indigo-500 text-indigo-500 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            Import Holidays
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showWeekendConfigForm() {
            document.getElementById('weekendConfigModal').classList.remove('hidden');
        }

        function hideWeekendConfigForm() {
            document.getElementById('weekendConfigModal').classList.add('hidden');
        }

        function showAddHolidayForm() {
            document.getElementById('addHolidayModal').classList.remove('hidden');
        }

        function hideAddHolidayForm() {
            document.getElementById('addHolidayModal').classList.add('hidden');
        }

        function showWeekendTemplatesForm() {
            document.getElementById('weekendTemplatesModal').classList.remove('hidden');
        }

        function hideWeekendTemplatesForm() {
            document.getElementById('weekendTemplatesModal').classList.add('hidden');
        }

        function showBulkConfigForm() {
            document.getElementById('bulkConfigModal').classList.remove('hidden');
        }

        function hideBulkConfigForm() {
            document.getElementById('bulkConfigModal').classList.add('hidden');
        }

        function applyTemplate(weekendDays, description) {
            // Close templates modal
            hideWeekendTemplatesForm();

            // Open weekend config modal with template applied
            showWeekendConfigForm();

            // Clear all checkboxes first
            const checkboxes = document.querySelectorAll('input[name="weekend_days[]"]');
            checkboxes.forEach(cb => cb.checked = false);

            // Check the template days
            weekendDays.forEach(day => {
                const checkbox = document.querySelector(`input[name="weekend_days[]"][value="${day}"]`);
                if (checkbox) checkbox.checked = true;
            });

            // Set description
            const descriptionField = document.querySelector('textarea[name="description"]');
            if (descriptionField) descriptionField.value = description;
        }

        function showHolidayTemplatesForm() {
            document.getElementById('holidayTemplatesModal').classList.remove('hidden');
        }

        function hideHolidayTemplatesForm() {
            document.getElementById('holidayTemplatesModal').classList.add('hidden');
        }

        function showBulkHolidayForm() {
            document.getElementById('bulkHolidayModal').classList.remove('hidden');
        }

        function hideBulkHolidayForm() {
            document.getElementById('bulkHolidayModal').classList.add('hidden');
        }

        function applyHolidayTemplate(templateType) {
            hideHolidayTemplatesForm();

            // Define holiday templates
            const templates = {
                'us_federal': [
                    { date: '{{ $monthSummary["year"] }}-01-01', name: 'New Year\'s Day', type: 'fixed' },
                    { date: '{{ $monthSummary["year"] }}-07-04', name: 'Independence Day', type: 'fixed' },
                    { date: '{{ $monthSummary["year"] }}-11-28', name: 'Thanksgiving Day', type: 'variable' },
                    { date: '{{ $monthSummary["year"] }}-12-25', name: 'Christmas Day', type: 'fixed' }
                ],
                'religious': [
                    { date: '{{ $monthSummary["year"] }}-12-25', name: 'Christmas Day', type: 'fixed' },
                    { date: '{{ $monthSummary["year"] }}-04-07', name: 'Good Friday', type: 'variable' },
                    { date: '{{ $monthSummary["year"] }}-04-09', name: 'Easter Sunday', type: 'variable' }
                ],
                'international': [
                    { date: '{{ $monthSummary["year"] }}-01-01', name: 'New Year\'s Day', type: 'fixed' },
                    { date: '{{ $monthSummary["year"] }}-05-01', name: 'Labor Day', type: 'fixed' },
                    { date: '{{ $monthSummary["year"] }}-12-25', name: 'Christmas Day', type: 'fixed' }
                ],
                'company': [
                    { date: '{{ $monthSummary["year"] }}-03-15', name: 'Founder\'s Day', type: 'custom' },
                    { date: '{{ $monthSummary["year"] }}-06-01', name: 'Company Anniversary', type: 'custom' },
                    { date: '{{ $monthSummary["year"] }}-09-15', name: 'Team Building Day', type: 'custom' }
                ]
            };

            const holidays = templates[templateType] || [];

            if (holidays.length > 0 && confirm(`Add ${holidays.length} holidays from ${templateType.replace('_', ' ')} template?`)) {
                // Add holidays one by one
                holidays.forEach(holiday => {
                    fetch('{{ route("admin.holiday.add") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            holiday_date: holiday.date,
                            name: holiday.name,
                            description: `Added from ${templateType.replace('_', ' ')} template`,
                            type: holiday.type
                        })
                    });
                });

                setTimeout(() => {
                    alert('Holiday template applied successfully!');
                    location.reload();
                }, 1000);
            }
        }

        // Handle custom range toggle
        document.addEventListener('DOMContentLoaded', function() {
            const applyToSelect = document.querySelector('select[name="apply_to"]');
            const customRangeFields = document.getElementById('customRangeFields');

            if (applyToSelect) {
                applyToSelect.addEventListener('change', function() {
                    if (this.value === 'custom_range') {
                        customRangeFields.classList.remove('hidden');
                    } else {
                        customRangeFields.classList.add('hidden');
                    }
                });
            }

            // Handle bulk holiday import method toggle
            const importMethodSelect = document.querySelector('select[name="import_method"]');
            const csvSection = document.getElementById('csvUploadSection');
            const manualSection = document.getElementById('manualEntrySection');
            const yearTemplateSection = document.getElementById('yearTemplateSection');

            if (importMethodSelect) {
                importMethodSelect.addEventListener('change', function() {
                    csvSection.classList.add('hidden');
                    manualSection.classList.add('hidden');
                    yearTemplateSection.classList.add('hidden');

                    switch(this.value) {
                        case 'csv':
                            csvSection.classList.remove('hidden');
                            break;
                        case 'manual':
                            manualSection.classList.remove('hidden');
                            break;
                        case 'year_template':
                            yearTemplateSection.classList.remove('hidden');
                            break;
                    }
                });
            }
        });

        document.getElementById('weekendConfigForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Convert weekend_days to array
            const weekendDays = formData.getAll('weekend_days[]').map(day => parseInt(day));
            data.weekend_days = weekendDays;
            
            fetch('{{ route("admin.weekend-config.set") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving weekend configuration.');
            });
        });

        document.getElementById('addHolidayForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            fetch('{{ route("admin.holiday.add") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding holiday.');
            });
        });

        function removeHoliday(holidayId) {
            if (confirm('Are you sure you want to remove this holiday?')) {
                fetch('{{ route("admin.holiday.remove") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ holiday_id: holidayId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while removing holiday.');
                });
            }
        }

        document.getElementById('bulkConfigForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Convert weekend_days to array
            const weekendDays = formData.getAll('bulk_weekend_days[]').map(day => parseInt(day));
            data.weekend_days = weekendDays;

            // Debug logging
            console.log('Form data:', data);
            console.log('Weekend days:', weekendDays);

            if (weekendDays.length === 0) {
                alert('Please select at least one weekend day.');
                return;
            }

            if (weekendDays.length >= 7) {
                alert('Cannot set all days as weekends. At least one business day is required.');
                return;
            }

            if (confirm('Are you sure you want to apply this configuration to multiple months? This will overwrite existing configurations.')) {
                fetch('{{ route("admin.weekend-config.bulk-set") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        let errorMessage = 'Error: ' + data.message;
                        if (data.errors && Array.isArray(data.errors)) {
                            errorMessage += '\n\nDetails:\n' + data.errors.join('\n');
                        } else if (data.errors && typeof data.errors === 'object') {
                            errorMessage += '\n\nDetails:\n' + Object.values(data.errors).flat().join('\n');
                        }
                        alert(errorMessage);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while applying bulk configuration: ' + error.message);
                });
            }
        });
    </script>
</x-app-layout>
