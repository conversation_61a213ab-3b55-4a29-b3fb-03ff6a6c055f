<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Weekend Configuration Management') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Navigation -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex justify-between items-center">
                        <a href="{{ route('admin.business-days') }}" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                            </svg>
                            Back to Business Days
                        </a>
                        
                        <div class="flex space-x-2">
                            <button onclick="showBulkConfigForm()" class="inline-flex items-center px-3 py-1.5 border border-indigo-500 text-indigo-500 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z" />
                                </svg>
                                Bulk Configure
                            </button>
                            <button onclick="showWeekendTemplatesForm()" class="inline-flex items-center px-3 py-1.5 border border-purple-500 text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                                </svg>
                                Templates
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Year Selection -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Select Year to Manage</h3>
                    
                    <form method="GET" action="{{ route('admin.weekend-config') }}" class="flex space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                            <select name="year" class="mt-1 block border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @for($y = 2020; $y <= 2030; $y++)
                                    <option value="{{ $y }}" {{ $y == $selectedYear ? 'selected' : '' }}>{{ $y }}</option>
                                @endfor
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                </svg>
                                View Year
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Monthly Weekend Configurations -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Weekend Configurations for {{ $selectedYear }}</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @for($month = 1; $month <= 12; $month++)
                            @php
                                $monthName = DateTime::createFromFormat('!m', $month)->format('F');
                                $config = $weekendConfigs->where('month', $month)->first();
                                $weekendDays = $config ? $config->weekend_days : [0, 6];
                                $dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                            @endphp
                            
                            <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex justify-between items-center mb-3">
                                    <h4 class="font-semibold text-gray-900 dark:text-gray-100">{{ $monthName }}</h4>
                                    <button onclick="configureMonth({{ $selectedYear }}, {{ $month }}, '{{ $monthName }}')" 
                                            class="inline-flex items-center px-2 py-1 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-xs font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-blue-500">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                        Edit
                                    </button>
                                </div>
                                
                                <!-- Week Preview -->
                                <div class="grid grid-cols-7 gap-1 text-center text-xs mb-3">
                                    @foreach($dayNames as $index => $dayName)
                                        <div class="p-1 rounded {{ in_array($index, $weekendDays) ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' }}">
                                            {{ $dayName }}
                                        </div>
                                    @endforeach
                                </div>
                                
                                <!-- Configuration Info -->
                                @if($config)
                                    <div class="text-xs text-gray-600 dark:text-gray-400">
                                        <p><strong>Weekend Days:</strong> {{ implode(', ', $config->weekend_day_names) }}</p>
                                        @if($config->description)
                                            <p class="mt-1 text-gray-500 dark:text-gray-500">{{ Str::limit($config->description, 50) }}</p>
                                        @endif
                                        <p class="mt-1 text-gray-400 dark:text-gray-600">Updated: {{ $config->updated_at->format('M j') }}</p>
                                    </div>
                                @else
                                    <div class="text-xs text-gray-500 dark:text-gray-500">
                                        <p>Using default configuration</p>
                                        <p>Weekend: Saturday & Sunday</p>
                                    </div>
                                @endif
                            </div>
                        @endfor
                    </div>
                </div>
            </div>

            <!-- Configuration Statistics -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Configuration Statistics for {{ $selectedYear }}</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded">
                            <div class="text-2xl font-bold text-blue-600">{{ $weekendConfigs->count() }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Custom Configurations</div>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded">
                            <div class="text-2xl font-bold text-green-600">{{ 12 - $weekendConfigs->count() }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Default Configurations</div>
                        </div>
                        <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded">
                            @php
                                $uniqueConfigs = $weekendConfigs->groupBy(function($item) {
                                    return implode(',', $item->weekend_days);
                                })->count();
                            @endphp
                            <div class="text-2xl font-bold text-purple-600">{{ $uniqueConfigs }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Unique Patterns</div>
                        </div>
                        <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded">
                            @php
                                $recentUpdates = $weekendConfigs->where('updated_at', '>=', now()->subDays(30))->count();
                            @endphp
                            <div class="text-2xl font-bold text-orange-600">{{ $recentUpdates }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Recent Updates (30d)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Month Configuration Modal -->
    <div id="monthConfigModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Configure Weekend for <span id="monthName"></span></h3>
                <form id="monthConfigForm">
                    <input type="hidden" name="year" id="configYear">
                    <input type="hidden" name="month" id="configMonth">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Weekend Days</label>
                        <div class="space-y-2">
                            @foreach(['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'] as $index => $day)
                                <label class="flex items-center">
                                    <input type="checkbox" name="weekend_days[]" value="{{ $index }}" 
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $day }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description (Optional)</label>
                        <textarea name="description" rows="2" 
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  placeholder="e.g., Special weekend configuration"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="hideMonthConfigForm()" 
                                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Save Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function configureMonth(year, month, monthName) {
            document.getElementById('monthName').textContent = monthName;
            document.getElementById('configYear').value = year;
            document.getElementById('configMonth').value = month;
            
            // Load existing configuration if any
            loadMonthConfiguration(year, month);
            
            document.getElementById('monthConfigModal').classList.remove('hidden');
        }

        function hideMonthConfigForm() {
            document.getElementById('monthConfigModal').classList.add('hidden');
        }

        function loadMonthConfiguration(year, month) {
            // Get existing configuration from the page data
            const configs = @json($weekendConfigs);
            const config = configs.find(c => c.month === month);
            
            // Clear all checkboxes first
            const checkboxes = document.querySelectorAll('#monthConfigModal input[name="weekend_days[]"]');
            checkboxes.forEach(cb => cb.checked = false);
            
            // Set weekend days
            const weekendDays = config ? config.weekend_days : [0, 6];
            weekendDays.forEach(day => {
                const checkbox = document.querySelector(`#monthConfigModal input[name="weekend_days[]"][value="${day}"]`);
                if (checkbox) checkbox.checked = true;
            });
            
            // Set description
            const descriptionField = document.querySelector('#monthConfigModal textarea[name="description"]');
            if (descriptionField) {
                descriptionField.value = config ? (config.description || '') : '';
            }
        }

        document.getElementById('monthConfigForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Convert weekend_days to array
            const weekendDays = formData.getAll('weekend_days[]').map(day => parseInt(day));
            data.weekend_days = weekendDays;
            
            if (weekendDays.length === 0) {
                alert('Please select at least one weekend day.');
                return;
            }
            
            fetch('{{ route("admin.weekend-config.set") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving weekend configuration.');
            });
        });

        // Placeholder functions for templates and bulk config (can reuse from main page)
        function showWeekendTemplatesForm() {
            alert('Templates feature - redirect to main weekend/holiday config page for full template functionality');
        }

        function showBulkConfigForm() {
            alert('Bulk configuration feature - redirect to main weekend/holiday config page for full bulk functionality');
        }
    </script>
</x-app-layout>
