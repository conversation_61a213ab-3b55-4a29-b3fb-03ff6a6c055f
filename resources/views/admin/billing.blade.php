<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Billing Management') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Navigation -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <a href="{{ route('admin.index') }}" class="bg-gray-50 dark:bg-gray-700 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        ← Back to Admin Dashboard
                    </a>
                </div>
            </div>

            <!-- Month/Year Filter and Generate Bills -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium">Monthly Billing</h3>
                        <button onclick="showGenerateBillsForm()" class="inline-flex items-center px-3 py-1.5 border border-green-500 text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 0a9 9 0 1118 0 9 9 0 01-18 0z" />
                            </svg>
                            Generate Bills
                        </button>
                    </div>
                    
                    <form method="GET" action="{{ route('admin.billing') }}" class="flex space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                            <select name="year" class="mt-1 block border-gray-300 dark:border-gray-600 rounded-md shadow-sm">
                                @for($y = 2020; $y <= 2030; $y++)
                                    <option value="{{ $y }}" {{ $summary['year'] == $y ? 'selected' : '' }}>{{ $y }}</option>
                                @endfor
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Month</label>
                            <select name="month" class="mt-1 block border-gray-300 dark:border-gray-600 rounded-md shadow-sm">
                                @for($m = 1; $m <= 12; $m++)
                                    <option value="{{ $m }}" {{ $summary['month'] == $m ? 'selected' : '' }}>
                                        {{ DateTime::createFromFormat('!m', $m)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Monthly Summary -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">{{ $summary['month_name'] }} Summary</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-blue-50 p-4 rounded">
                            <div class="text-2xl font-bold text-blue-600">{{ $summary['total_users'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Total Users</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded">
                            <div class="text-2xl font-bold text-green-600">{{ $summary['total_lunches'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Total Lunches</div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded">
                            <div class="text-2xl font-bold text-purple-600">৳{{ number_format($summary['lunch_price'], 2) }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Lunch Price</div>
                        </div>
                        <div class="bg-orange-50 p-4 rounded">
                            <div class="text-2xl font-bold text-orange-600">৳{{ number_format($summary['total_amount'], 2) }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Total Amount</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bills List -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Individual Bills - {{ $summary['month_name'] }}</h3>
                    
                    @if($summary['bills']->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700">
                                        <th class="px-4 py-2 text-left">User</th>
                                        <th class="px-4 py-2 text-left">Email</th>
                                        <th class="px-4 py-2 text-left">Mode</th>
                                        <th class="px-4 py-2 text-left">Lunch Count</th>
                                        <th class="px-4 py-2 text-left">Rate</th>
                                        <th class="px-4 py-2 text-left">Amount</th>
                                        <th class="px-4 py-2 text-left">Generated</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($summary['bills'] as $bill)
                                    <tr class="border-b">
                                        <td class="px-4 py-2 font-medium">{{ $bill->user->name }}</td>
                                        <td class="px-4 py-2">{{ $bill->user->email }}</td>
                                        <td class="px-4 py-2">
                                            <span class="capitalize px-2 py-1 rounded text-sm {{ $bill->user->mode === 'regular' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                                {{ $bill->user->mode }}
                                            </span>
                                        </td>
                                        <td class="px-4 py-2">
                                            <span class="font-bold">{{ $bill->lunch_count }}</span>
                                        </td>
                                        <td class="px-4 py-2">৳{{ number_format($bill->rate, 2) }}</td>
                                        <td class="px-4 py-2">
                                            <span class="font-bold text-green-600">৳{{ number_format($bill->amount, 2) }}</span>
                                        </td>
                                        <td class="px-4 py-2">{{ $bill->updated_at->format('M j, Y') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="text-gray-500 dark:text-gray-400 text-lg">No bills generated for this month yet.</div>
                            <p class="text-gray-400 mt-2">Click "Generate Bills" to create bills for all users for this month.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Generate Bills Modal -->
    <div id="generateBillsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Generate Bills</h3>
                <form id="generateBillsForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                        <select name="year" required class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @for($y = 2020; $y <= 2030; $y++)
                                <option value="{{ $y }}" {{ $y == $summary['year'] ? 'selected' : '' }}>{{ $y }}</option>
                            @endfor
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Month</label>
                        <select name="month" required class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @for($m = 1; $m <= 12; $m++)
                                <option value="{{ $m }}" {{ $m == $summary['month'] ? 'selected' : '' }}>
                                    {{ DateTime::createFromFormat('!m', $m)->format('F') }}
                                </option>
                            @endfor
                        </select>
                    </div>
                    <div class="mb-4 bg-yellow-50 border border-yellow-200 rounded p-3">
                        <p class="text-sm text-yellow-800">
                            <strong>Note:</strong> This will generate or update bills for all users based on their lunch orders for the selected month.
                        </p>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="hideGenerateBillsForm()"
                                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-green-500 text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 0a9 9 0 1118 0 9 9 0 01-18 0z" />
                            </svg>
                            Generate Bills
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showGenerateBillsForm() {
            document.getElementById('generateBillsModal').classList.remove('hidden');
        }

        function hideGenerateBillsForm() {
            document.getElementById('generateBillsModal').classList.add('hidden');
        }

        document.getElementById('generateBillsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            fetch('{{ route("admin.billing.generate") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while generating bills.');
            });
        });
    </script>
</x-app-layout>
