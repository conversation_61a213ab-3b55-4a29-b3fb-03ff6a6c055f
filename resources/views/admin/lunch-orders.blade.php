<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 dark:text-gray-200 leading-tight">
            {{ __('Lunch Orders Management') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Navigation -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <a href="{{ route('admin.index') }}" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200">
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Back to Admin Dashboard
                    </a>
                </div>
            </div>

            <!-- Date Filter -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Filter by Date</h3>
                    <form method="GET" action="{{ route('admin.lunch-orders') }}" class="flex space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date</label>
                            <input type="date" name="date" value="{{ $date->format('Y-m-d') }}"
                                   class="mt-1 block border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                </svg>
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div class="flex items-center">
                            <div class="text-3xl font-bold text-green-600">{{ $orderedLunches->count() }}</div>
                            <div class="ml-4">
                                <div class="text-sm text-gray-600 dark:text-gray-400">Ordered Lunches</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ $date->format('F j, Y') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div class="flex items-center">
                            <div class="text-3xl font-bold text-red-600">{{ $cancelledLunches->count() }}</div>
                            <div class="ml-4">
                                <div class="text-sm text-gray-600 dark:text-gray-400">Cancelled Lunches</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ $date->format('F j, Y') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div class="flex items-center">
                            <div class="text-3xl font-bold text-blue-600">{{ $orderedLunches->count() + $cancelledLunches->count() }}</div>
                            <div class="ml-4">
                                <div class="text-sm text-gray-600 dark:text-gray-400">Total Users</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">With lunch records</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ordered Lunches -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">
                        Ordered Lunches - {{ $date->format('F j, Y') }}
                    </h3>
                    
                    @if($orderedLunches->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700">
                                        <th class="px-4 py-2 text-left">Name</th>
                                        <th class="px-4 py-2 text-left">Email</th>
                                        <th class="px-4 py-2 text-left">Mode</th>
                                        <th class="px-4 py-2 text-left">Order Time</th>
                                        <th class="px-4 py-2 text-left">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($orderedLunches as $order)
                                    <tr class="border-b">
                                        <td class="px-4 py-2 font-medium">{{ $order->user->name }}</td>
                                        <td class="px-4 py-2">{{ $order->user->email }}</td>
                                        <td class="px-4 py-2">
                                            <span class="capitalize px-2 py-1 rounded text-sm {{ $order->user->mode === 'regular' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                                {{ $order->user->mode }}
                                            </span>
                                        </td>
                                        <td class="px-4 py-2">{{ $order->created_at->format('H:i:s') }}</td>
                                        <td class="px-4 py-2">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">
                                                ✓ Ordered
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="text-gray-500 dark:text-gray-400 text-lg">No ordered lunches for this date.</div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Cancelled Lunches -->
            @if($cancelledLunches->count() > 0)
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">
                        Cancelled Lunches - {{ $date->format('F j, Y') }}
                    </h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full table-auto">
                            <thead>
                                <tr class="bg-gray-50 dark:bg-gray-700">
                                    <th class="px-4 py-2 text-left">Name</th>
                                    <th class="px-4 py-2 text-left">Email</th>
                                    <th class="px-4 py-2 text-left">Mode</th>
                                    <th class="px-4 py-2 text-left">Cancelled Time</th>
                                    <th class="px-4 py-2 text-left">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($cancelledLunches as $order)
                                <tr class="border-b">
                                    <td class="px-4 py-2 font-medium">{{ $order->user->name }}</td>
                                    <td class="px-4 py-2">{{ $order->user->email }}</td>
                                    <td class="px-4 py-2">
                                        <span class="capitalize px-2 py-1 rounded text-sm {{ $order->user->mode === 'regular' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                            {{ $order->user->mode }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2">{{ $order->updated_at->format('H:i:s') }}</td>
                                    <td class="px-4 py-2">
                                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded text-sm">
                                            ✗ Cancelled
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</x-app-layout>
