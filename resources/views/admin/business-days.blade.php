<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Business Days Management') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Navigation -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <a href="{{ route('admin.index') }}" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200">
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Back to Admin Dashboard
                    </a>
                </div>
            </div>

            <!-- Month Filter -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium">Month Selection</h3>
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.weekend-holiday-config', ['year' => $monthSummary['year'], 'month' => $monthSummary['month']]) }}" class="inline-flex items-center px-3 py-1.5 border border-purple-500 text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                Configure Weekends & Holidays
                            </a>
                            <button onclick="showCreateAutoOrdersForm()" class="inline-flex items-center px-3 py-1.5 border border-green-500 text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Create Monthly Auto Orders
                            </button>
                        </div>
                    </div>
                    
                    <form method="GET" action="{{ route('admin.business-days') }}" class="flex space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                            <select name="year" class="mt-1 block border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @for($y = 2020; $y <= 2030; $y++)
                                    <option value="{{ $y }}" {{ $y == $monthSummary['year'] ? 'selected' : '' }}>{{ $y }}</option>
                                @endfor
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Month</label>
                            <select name="month" class="mt-1 block border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @for($m = 1; $m <= 12; $m++)
                                    <option value="{{ $m }}" {{ $m == $monthSummary['month'] ? 'selected' : '' }}>
                                        {{ DateTime::createFromFormat('!m', $m)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                </svg>
                                View Month
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Month Summary -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium">{{ $monthSummary['month_name'] }} Summary</h3>
                        <div class="flex space-x-2">
                            <button onclick="showQuickActions()" class="inline-flex items-center px-3 py-1.5 border border-gray-500 text-gray-500 hover:bg-gray-50 dark:hover:bg-gray-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                Quick Actions
                            </button>
                            <button onclick="exportMonthData()" class="inline-flex items-center px-3 py-1.5 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Export Data
                            </button>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded cursor-pointer hover:shadow-md transition-shadow duration-200" onclick="showDayDetails('total')">
                            <div class="text-2xl font-bold text-blue-600">{{ $monthSummary['total_days'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Total Days</div>
                            <div class="text-xs text-blue-500 mt-1">Click for details</div>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded cursor-pointer hover:shadow-md transition-shadow duration-200" onclick="showDayDetails('business')">
                            <div class="text-2xl font-bold text-green-600">{{ $monthSummary['business_days_count'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Business Days</div>
                            <div class="text-xs text-green-500 mt-1">{{ round(($monthSummary['business_days_count'] / $monthSummary['total_days']) * 100) }}% of month</div>
                        </div>
                        <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded cursor-pointer hover:shadow-md transition-shadow duration-200" onclick="showDayDetails('weekends')">
                            <div class="text-2xl font-bold text-orange-600">{{ $monthSummary['weekends_count'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Weekend Days</div>
                            <div class="text-xs text-orange-500 mt-1">{{ round(($monthSummary['weekends_count'] / $monthSummary['total_days']) * 100) }}% of month</div>
                        </div>
                        <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded cursor-pointer hover:shadow-md transition-shadow duration-200" onclick="showDayDetails('holidays')">
                            <div class="text-2xl font-bold text-red-600">{{ $monthSummary['holidays_count'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Holidays</div>
                            <div class="text-xs text-red-500 mt-1">{{ $monthSummary['holidays_count'] > 0 ? round(($monthSummary['holidays_count'] / $monthSummary['total_days']) * 100) : 0 }}% of month</div>
                        </div>
                        <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded cursor-pointer hover:shadow-md transition-shadow duration-200" onclick="showOrderDetails()">
                            <div class="text-2xl font-bold text-purple-600">{{ $monthSummary['total_orders'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Total Orders</div>
                            <div class="text-xs text-purple-500 mt-1">
                                @if($monthSummary['business_days_count'] > 0)
                                    {{ round($monthSummary['total_orders'] / $monthSummary['business_days_count'], 1) }} avg/day
                                @else
                                    No business days
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuration Status -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Configuration Status</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Weekend Configuration</h4>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-300">
                                    Active
                                </span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                Current weekend pattern for this month
                            </p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500">Last updated: Recently</span>
                                <a href="{{ route('admin.weekend-holiday-config', ['year' => $monthSummary['year'], 'month' => $monthSummary['month']]) }}"
                                   class="inline-flex items-center px-2 py-1 border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-xs font-medium rounded transition-colors duration-200">
                                    Configure
                                </a>
                            </div>
                        </div>

                        <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Holiday Configuration</h4>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $monthSummary['holidays_count'] > 0 ? 'bg-green-100 dark:bg-green-900/40 text-green-800 dark:text-green-300' : 'bg-gray-100 dark:bg-gray-900/40 text-gray-800 dark:text-gray-300' }}">
                                    {{ $monthSummary['holidays_count'] > 0 ? 'Configured' : 'None Set' }}
                                </span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                {{ $monthSummary['holidays_count'] }} holiday(s) configured for this month
                            </p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500">{{ $monthSummary['holidays_count'] }} active</span>
                                <a href="{{ route('admin.weekend-holiday-config', ['year' => $monthSummary['year'], 'month' => $monthSummary['month']]) }}"
                                   class="inline-flex items-center px-2 py-1 border border-green-500 text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 text-xs font-medium rounded transition-colors duration-200">
                                    Manage
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Days Calendar -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-6">Business Days Calendar</h3>

                    <!-- Traditional Calendar Grid -->
                    <div class="mb-6">
                        @php
                            $startOfMonth = \Carbon\Carbon::create($monthSummary['year'], $monthSummary['month'], 1);
                            $endOfMonth = $startOfMonth->copy()->endOfMonth();
                            $startOfCalendar = $startOfMonth->copy()->startOfWeek();
                            $endOfCalendar = $endOfMonth->copy()->endOfWeek();

                            $businessDayDates = $monthSummary['business_days']->map(function($date) {
                                return $date->format('Y-m-d');
                            })->toArray();

                            $weekendDates = $monthSummary['weekends']->map(function($date) {
                                return $date->format('Y-m-d');
                            })->toArray();

                            $holidayDates = $monthSummary['holidays']->map(function($date) {
                                return $date->format('Y-m-d');
                            })->toArray();
                        @endphp

                        <!-- Calendar Headers -->
                        <div class="grid grid-cols-7 gap-2 mb-4">
                            <div class="text-center font-medium text-gray-600 dark:text-gray-400 py-2 text-sm">Sun</div>
                            <div class="text-center font-medium text-gray-600 dark:text-gray-400 py-2 text-sm">Mon</div>
                            <div class="text-center font-medium text-gray-600 dark:text-gray-400 py-2 text-sm">Tue</div>
                            <div class="text-center font-medium text-gray-600 dark:text-gray-400 py-2 text-sm">Wed</div>
                            <div class="text-center font-medium text-gray-600 dark:text-gray-400 py-2 text-sm">Thu</div>
                            <div class="text-center font-medium text-gray-600 dark:text-gray-400 py-2 text-sm">Fri</div>
                            <div class="text-center font-medium text-gray-600 dark:text-gray-400 py-2 text-sm">Sat</div>
                        </div>

                        <!-- Calendar Days Grid -->
                        <div class="grid grid-cols-7 gap-2">
                            @for($date = $startOfCalendar->copy(); $date->lte($endOfCalendar); $date->addDay())
                                @php
                                    $dateStr = $date->format('Y-m-d');
                                    $isCurrentMonth = $date->month == $monthSummary['month'];
                                    $isBusinessDay = in_array($dateStr, $businessDayDates);
                                    $isWeekend = in_array($dateStr, $weekendDates);
                                    $isHoliday = in_array($dateStr, $holidayDates);
                                    $isToday = $date->isToday();

                                    // Find holiday configuration for this date
                                    $holidayConfig = null;
                                    if ($isHoliday && $isCurrentMonth) {
                                        $holidayConfig = $monthSummary['holiday_configs']->first(function($config) use ($dateStr) {
                                            return $config->holiday_date->format('Y-m-d') === $dateStr;
                                        });
                                    }

                                    // Determine styling based on day type - exactly like your image
                                    $bgClass = '';
                                    $textClass = '';
                                    $indicator = '';
                                    $dayLabel = '';

                                    if (!$isCurrentMonth) {
                                        $bgClass = 'bg-gray-50 dark:bg-gray-700';
                                        $textClass = 'text-gray-300 dark:text-gray-600';
                                    } elseif ($isBusinessDay) {
                                        $bgClass = 'bg-green-100 dark:bg-green-900/30';
                                        $textClass = 'text-green-800 dark:text-green-300';
                                        $indicator = '✓';
                                        $dayLabel = 'Business Day';
                                    } elseif ($isHoliday) {
                                        $bgClass = 'bg-orange-100 dark:bg-orange-900/30';
                                        $textClass = 'text-orange-800 dark:text-orange-300';
                                        $indicator = 'H';
                                        $dayLabel = $holidayConfig ? $holidayConfig->name : 'Holiday';
                                    } elseif ($isWeekend) {
                                        $bgClass = 'bg-blue-50 dark:bg-blue-900/30';
                                        $textClass = 'text-blue-600 dark:text-blue-400';
                                        $indicator = 'W';
                                        $dayLabel = 'Weekend';
                                    } else {
                                        // Regular days
                                        $bgClass = 'bg-gray-50 dark:bg-gray-700';
                                        $textClass = 'text-gray-600 dark:text-gray-400';
                                        $dayLabel = 'Regular Day';
                                    }

                                    $borderClass = $isToday ? 'border-2 border-blue-500' : 'border border-gray-200 dark:border-gray-600';
                                @endphp

                                <div class="relative h-20 {{ $bgClass }} {{ $textClass }} {{ $borderClass }} rounded flex flex-col items-center justify-center text-center transition-all duration-200 hover:shadow-md cursor-pointer group"
                                     title="{{ $isCurrentMonth ? $dayLabel : '' }}"
                                     @if($isCurrentMonth) onclick="showDayInfo('{{ $date->format('Y-m-d') }}', '{{ $dayLabel }}', '{{ $indicator }}', {{ $isHoliday && $holidayConfig ? "'" . addslashes($holidayConfig->name) . "'" : 'null' }}, {{ $isHoliday && $holidayConfig && $holidayConfig->description ? "'" . addslashes($holidayConfig->description) . "'" : 'null' }})" @endif>

                                    <!-- Day Number -->
                                    <div class="text-sm font-medium">{{ $date->day }}</div>

                                    <!-- Day Type Indicator -->
                                    @if($indicator && $isCurrentMonth)
                                        <div class="text-xs mt-0.5 font-semibold">{{ $indicator }}</div>
                                    @endif

                                    <!-- Holiday Name (truncated) -->
                                    @if($isHoliday && $holidayConfig && $isCurrentMonth)
                                        <div class="text-xs mt-0.5 px-1 leading-tight max-w-full overflow-hidden">
                                            <div class="truncate">{{ Str::limit($holidayConfig->name, 8) }}</div>
                                        </div>
                                    @endif

                                    <!-- Weekend Day Name -->
                                    @if($isWeekend && $isCurrentMonth && !$isHoliday)
                                        <div class="text-xs mt-0.5 opacity-75">
                                            {{ $date->format('D') }}
                                        </div>
                                    @endif

                                    <!-- Today Indicator -->
                                    @if($isToday)
                                        <div class="absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full"></div>
                                    @endif

                                    <!-- Hover Tooltip -->
                                    @if($isCurrentMonth)
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                                            @if($isHoliday && $holidayConfig)
                                                {{ $holidayConfig->name }}
                                            @elseif($isWeekend)
                                                Weekend ({{ $date->format('l') }})
                                            @elseif($isBusinessDay)
                                                Business Day ({{ $date->format('l') }})
                                            @else
                                                {{ $date->format('l') }}
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            @endfor
                        </div>
                    </div>

                    <!-- Legend -->
                    <div class="flex flex-wrap gap-6 text-sm mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-100 dark:bg-green-900/30 rounded mr-2"></div>
                            <span class="text-gray-700 dark:text-gray-300">Business Days (✓)</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-orange-100 dark:bg-orange-900/30 rounded mr-2"></div>
                            <span class="text-gray-700 dark:text-gray-300">Holidays (H)</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-50 dark:bg-blue-900/30 rounded mr-2"></div>
                            <span class="text-gray-700 dark:text-gray-300">Weekends (W)</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gray-50 dark:bg-gray-700 rounded mr-2"></div>
                            <span class="text-gray-700 dark:text-gray-300">Regular Days</span>
                        </div>
                    </div>

                    <!-- Calendar Instructions -->
                    <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                        <p class="text-sm text-blue-800 dark:text-blue-300">
                            <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            <strong>Click on any day</strong> to view detailed information about holidays, weekends, and business day configurations.
                        </p>
                    </div>

                    <!-- Current Month Configuration Details -->
                    <div class="mt-6 space-y-4">
                        <!-- Configured Holidays Section -->
                        @if($monthSummary['holidays_count'] > 0)
                            <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-orange-800 dark:text-orange-300 mb-3 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                    Configured Holidays for {{ $monthSummary['month_name'] }} ({{ $monthSummary['holidays_count'] }} days)
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    @foreach($monthSummary['holiday_configs'] as $holidayConfig)
                                        <div class="bg-white dark:bg-gray-800 border border-orange-200 dark:border-orange-600 rounded p-3">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ $holidayConfig->name }}
                                            </div>
                                            <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">
                                                {{ $holidayConfig->holiday_date->format('M j, Y') }} ({{ $holidayConfig->holiday_date->format('l') }})
                                            </div>
                                            @if($holidayConfig->description)
                                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    {{ $holidayConfig->description }}
                                                </div>
                                            @endif
                                            <div class="mt-2">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 dark:bg-orange-900/40 text-orange-800 dark:text-orange-300">
                                                    {{ ucfirst($holidayConfig->type) }}
                                                </span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                    No Holidays Configured for {{ $monthSummary['month_name'] }}
                                </h4>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    <a href="{{ route('admin.weekend-holiday-config', ['year' => $monthSummary['year'], 'month' => $monthSummary['month']]) }}"
                                       class="text-blue-600 dark:text-blue-400 hover:underline">
                                        Click here to configure holidays for this month
                                    </a>
                                </p>
                            </div>
                        @endif

                        <!-- Configured Weekends Section -->
                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-800 dark:text-blue-300 mb-3 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                                Configured Weekend Days for {{ $monthSummary['month_name'] }} ({{ $monthSummary['weekends_count'] }} days)
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                                @php
                                    $weekendsByDay = $monthSummary['weekends']->groupBy(function($date) {
                                        return $date->format('l'); // Group by day name
                                    });
                                @endphp
                                @foreach($weekendsByDay as $dayName => $dates)
                                    <div class="bg-white dark:bg-gray-800 border border-blue-200 dark:border-blue-600 rounded p-3">
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            {{ $dayName }}s
                                        </div>
                                        <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                            {{ $dates->count() }} days this month
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            @foreach($dates->take(3) as $date)
                                                {{ $date->format('j') }}{{ !$loop->last ? ', ' : '' }}
                                            @endforeach
                                            @if($dates->count() > 3)
                                                ...
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Month Summary Stats -->
                        <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ $monthSummary['month_name'] }} Summary
                            </h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div class="text-center">
                                    <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ $monthSummary['total_days'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Total Days</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-semibold text-green-600 dark:text-green-400">{{ $monthSummary['business_days_count'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Business Days</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-semibold text-blue-600 dark:text-blue-400">{{ $monthSummary['weekends_count'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Weekend Days</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-semibold text-orange-600 dark:text-orange-400">{{ $monthSummary['holidays_count'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Holiday Days</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Auto Orders Modal -->
    <div id="createAutoOrdersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Create Monthly Auto Orders</h3>
                <form id="createAutoOrdersForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year</label>
                        <select name="year" required class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @for($y = 2020; $y <= 2030; $y++)
                                <option value="{{ $y }}" {{ $y == $monthSummary['year'] ? 'selected' : '' }}>{{ $y }}</option>
                            @endfor
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Month</label>
                        <select name="month" required class="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @for($m = 1; $m <= 12; $m++)
                                <option value="{{ $m }}" {{ $m == $monthSummary['month'] ? 'selected' : '' }}>
                                    {{ DateTime::createFromFormat('!m', $m)->format('F') }}
                                </option>
                            @endfor
                        </select>
                    </div>
                    <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded p-3">
                        <p class="text-sm text-yellow-800 dark:text-yellow-300">
                            <strong>Note:</strong> This will create lunch orders for all regular users for all business days in the selected month.
                        </p>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="hideCreateAutoOrdersForm()" 
                                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-green-500 text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Create Auto Orders
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Actions Modal -->
    <div id="quickActionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4">Quick Actions for {{ $monthSummary['month_name'] }}</h3>
                <div class="space-y-3">
                    <button onclick="quickCreateAutoOrders()" class="w-full text-left border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <h4 class="font-semibold text-sm text-green-600">Create Auto Orders</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Generate lunch orders for all business days</p>
                    </button>

                    <button onclick="quickConfigureWeekends()" class="w-full text-left border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <h4 class="font-semibold text-sm text-blue-600">Configure Weekends</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Set custom weekend days for this month</p>
                    </button>

                    <button onclick="quickAddHoliday()" class="w-full text-left border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <h4 class="font-semibold text-sm text-red-600">Add Holiday</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Add a holiday to this month</p>
                    </button>

                    <button onclick="quickViewOrders()" class="w-full text-left border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <h4 class="font-semibold text-sm text-purple-600">View All Orders</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">See all lunch orders for this month</p>
                    </button>

                    <button onclick="quickGenerateReport()" class="w-full text-left border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <h4 class="font-semibold text-sm text-indigo-600">Generate Report</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Create detailed business days report</p>
                    </button>
                </div>

                <div class="flex justify-end space-x-2 mt-4">
                    <button type="button" onclick="hideQuickActions()"
                            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Day Details Modal -->
    <div id="dayDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
                <h3 class="text-lg font-medium mb-4" id="dayDetailsTitle">Day Details</h3>
                <div id="dayDetailsContent" class="space-y-3">
                    <!-- Content will be populated by JavaScript -->
                </div>

                <div class="flex justify-end space-x-2 mt-4">
                    <button type="button" onclick="hideDayDetails()"
                            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm font-medium rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showCreateAutoOrdersForm() {
            document.getElementById('createAutoOrdersModal').classList.remove('hidden');
        }

        function hideCreateAutoOrdersForm() {
            document.getElementById('createAutoOrdersModal').classList.add('hidden');
        }

        function showQuickActions() {
            document.getElementById('quickActionsModal').classList.remove('hidden');
        }

        function hideQuickActions() {
            document.getElementById('quickActionsModal').classList.add('hidden');
        }

        function showDayDetails(type) {
            const title = document.getElementById('dayDetailsTitle');
            const content = document.getElementById('dayDetailsContent');

            const monthData = {
                total: {{ $monthSummary['total_days'] }},
                business: {{ $monthSummary['business_days_count'] }},
                weekends: {{ $monthSummary['weekends_count'] }},
                holidays: {{ $monthSummary['holidays_count'] }}
            };

            switch(type) {
                case 'total':
                    title.textContent = 'Total Days Details';
                    content.innerHTML = `
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded">
                            <p class="text-sm"><strong>Total Days:</strong> ${monthData.total}</p>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Complete month overview</p>
                        </div>
                        <div class="text-sm space-y-1">
                            <p>• Business Days: ${monthData.business} (${Math.round((monthData.business / monthData.total) * 100)}%)</p>
                            <p>• Weekend Days: ${monthData.weekends} (${Math.round((monthData.weekends / monthData.total) * 100)}%)</p>
                            <p>• Holiday Days: ${monthData.holidays} (${Math.round((monthData.holidays / monthData.total) * 100)}%)</p>
                        </div>
                    `;
                    break;
                case 'business':
                    title.textContent = 'Business Days Details';
                    content.innerHTML = `
                        <div class="bg-green-50 dark:bg-green-900/20 p-3 rounded">
                            <p class="text-sm"><strong>Business Days:</strong> ${monthData.business}</p>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Days when lunch ordering is available</p>
                        </div>
                        <div class="text-sm space-y-1">
                            <p>• Percentage of month: ${Math.round((monthData.business / monthData.total) * 100)}%</p>
                            <p>• Average per week: ${Math.round(monthData.business / 4.3)}</p>
                            <p>• Potential lunch orders: ${monthData.business} days</p>
                        </div>
                    `;
                    break;
                case 'weekends':
                    title.textContent = 'Weekend Days Details';
                    content.innerHTML = `
                        <div class="bg-orange-50 dark:bg-orange-900/20 p-3 rounded">
                            <p class="text-sm"><strong>Weekend Days:</strong> ${monthData.weekends}</p>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Days configured as weekends</p>
                        </div>
                        <div class="text-sm space-y-1">
                            <p>• Percentage of month: ${Math.round((monthData.weekends / monthData.total) * 100)}%</p>
                            <p>• Average per week: ${Math.round(monthData.weekends / 4.3)}</p>
                            <p>• Configuration: Custom weekend pattern</p>
                        </div>
                    `;
                    break;
                case 'holidays':
                    title.textContent = 'Holiday Days Details';
                    content.innerHTML = `
                        <div class="bg-red-50 dark:bg-red-900/20 p-3 rounded">
                            <p class="text-sm"><strong>Holiday Days:</strong> ${monthData.holidays}</p>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Special non-working days</p>
                        </div>
                        <div class="text-sm space-y-1">
                            <p>• Percentage of month: ${Math.round((monthData.holidays / monthData.total) * 100)}%</p>
                            <p>• Impact on business days: ${monthData.holidays} days removed</p>
                            <p>• Status: ${monthData.holidays > 0 ? 'Configured' : 'None set'}</p>
                        </div>
                    `;
                    break;
            }

            document.getElementById('dayDetailsModal').classList.remove('hidden');
        }

        function hideDayDetails() {
            document.getElementById('dayDetailsModal').classList.add('hidden');
        }

        function showDayInfo(date, dayLabel, indicator, holidayName, holidayDescription) {
            const title = document.getElementById('dayDetailsTitle');
            const content = document.getElementById('dayDetailsContent');

            const dateObj = new Date(date);
            const formattedDate = dateObj.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            title.textContent = `Day Details - ${formattedDate}`;

            let contentHtml = `
                <div class="space-y-3">
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                        <p class="text-sm"><strong>Date:</strong> ${formattedDate}</p>
                        <p class="text-sm"><strong>Type:</strong> ${dayLabel}</p>
                        <p class="text-sm"><strong>Indicator:</strong> ${indicator || 'None'}</p>
                    </div>
            `;

            if (holidayName) {
                contentHtml += `
                    <div class="bg-orange-50 dark:bg-orange-900/20 p-3 rounded border border-orange-200 dark:border-orange-700">
                        <h4 class="font-semibold text-orange-800 dark:text-orange-300 mb-2">Holiday Information</h4>
                        <p class="text-sm"><strong>Holiday Name:</strong> ${holidayName}</p>
                        ${holidayDescription ? `<p class="text-sm mt-1"><strong>Description:</strong> ${holidayDescription}</p>` : ''}
                    </div>
                `;
            }

            if (dayLabel === 'Weekend') {
                contentHtml += `
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded border border-blue-200 dark:border-blue-700">
                        <h4 class="font-semibold text-blue-800 dark:text-blue-300 mb-2">Weekend Information</h4>
                        <p class="text-sm">This day is configured as a weekend day in your system.</p>
                        <p class="text-sm mt-1">No lunch orders can be placed for weekend days.</p>
                    </div>
                `;
            }

            if (dayLabel === 'Business Day') {
                contentHtml += `
                    <div class="bg-green-50 dark:bg-green-900/20 p-3 rounded border border-green-200 dark:border-green-700">
                        <h4 class="font-semibold text-green-800 dark:text-green-300 mb-2">Business Day Information</h4>
                        <p class="text-sm">This is a working day where lunch orders can be placed.</p>
                        <p class="text-sm mt-1">Orders must be placed before 10:00 AM on the same day.</p>
                    </div>
                `;
            }

            contentHtml += '</div>';
            content.innerHTML = contentHtml;

            document.getElementById('dayDetailsModal').classList.remove('hidden');
        }

        function showOrderDetails() {
            const title = document.getElementById('dayDetailsTitle');
            const content = document.getElementById('dayDetailsContent');

            title.textContent = 'Order Details';
            content.innerHTML = `
                <div class="bg-purple-50 dark:bg-purple-900/20 p-3 rounded">
                    <p class="text-sm"><strong>Total Orders:</strong> {{ $monthSummary['total_orders'] }}</p>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Lunch orders for this month</p>
                </div>
                <div class="text-sm space-y-1">
                    <p>• Average per business day: ${Math.round({{ $monthSummary['total_orders'] }} / {{ $monthSummary['business_days_count'] }}, 1)}</p>
                    <p>• Business days available: {{ $monthSummary['business_days_count'] }}</p>
                    <p>• Order coverage: ${Math.round(({{ $monthSummary['total_orders'] }} / {{ $monthSummary['business_days_count'] }}) * 100)}%</p>
                </div>
            `;

            document.getElementById('dayDetailsModal').classList.remove('hidden');
        }

        // Quick action functions
        function quickCreateAutoOrders() {
            hideQuickActions();
            showCreateAutoOrdersForm();
        }

        function quickConfigureWeekends() {
            hideQuickActions();
            window.location.href = '{{ route("admin.weekend-holiday-config", ["year" => $monthSummary["year"], "month" => $monthSummary["month"]]) }}';
        }

        function quickAddHoliday() {
            hideQuickActions();
            window.location.href = '{{ route("admin.weekend-holiday-config", ["year" => $monthSummary["year"], "month" => $monthSummary["month"]]) }}';
        }

        function quickViewOrders() {
            hideQuickActions();
            window.location.href = '{{ route("admin.orders") }}';
        }

        function quickGenerateReport() {
            hideQuickActions();
            exportMonthData();
        }

        function exportMonthData() {
            const data = {
                month: '{{ $monthSummary["month_name"] }}',
                total_days: {{ $monthSummary['total_days'] }},
                business_days: {{ $monthSummary['business_days_count'] }},
                weekends: {{ $monthSummary['weekends_count'] }},
                holidays: {{ $monthSummary['holidays_count'] }},
                total_orders: {{ $monthSummary['total_orders'] }}
            };

            const csvContent = "data:text/csv;charset=utf-8,"
                + "Month,Total Days,Business Days,Weekend Days,Holiday Days,Total Orders\n"
                + `${data.month},${data.total_days},${data.business_days},${data.weekends},${data.holidays},${data.total_orders}`;

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `business_days_${data.month.replace(' ', '_')}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        document.getElementById('createAutoOrdersForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            fetch('{{ route("admin.business-days.create-auto-orders") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while creating auto orders.');
            });
        });
    </script>
</x-app-layout>
