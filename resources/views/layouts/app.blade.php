<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Dark Mode Script -->
        <script>
            // Check for saved dark mode preference or default to light mode
            if (localStorage.getItem('darkMode') === 'true' || (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
            }
        </script>
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100 dark:bg-gray-900">
            @include('layouts.navigation')

            <!-- Page Heading -->
            @isset($header)
                <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endisset

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>
        </div>

        <!-- Dark Mode Toggle Script -->
        <script>
            function toggleDarkMode() {
                const isDark = document.documentElement.classList.contains('dark');

                if (isDark) {
                    document.documentElement.classList.remove('dark');
                    localStorage.setItem('darkMode', 'false');
                    updateDarkModeIcon(false);
                } else {
                    document.documentElement.classList.add('dark');
                    localStorage.setItem('darkMode', 'true');
                    updateDarkModeIcon(true);
                }
            }

            function updateDarkModeIcon(isDark) {
                // Desktop icons
                const moonIcon = document.getElementById('moonIcon');
                const sunIcon = document.getElementById('sunIcon');
                const darkModeIcon = document.getElementById('darkModeIcon');

                // Mobile icons
                const mobileMoonIcon = document.getElementById('mobileMoonIcon');
                const mobileSunIcon = document.getElementById('mobileSunIcon');
                const mobileDarkModeIcon = document.getElementById('mobileDarkModeIcon');
                const mobileDarkModeText = document.getElementById('mobileDarkModeText');

                // Update desktop icons
                if (moonIcon && sunIcon && darkModeIcon) {
                    darkModeIcon.style.transform = 'rotate(180deg)';

                    setTimeout(() => {
                        if (isDark) {
                            moonIcon.classList.add('hidden');
                            sunIcon.classList.remove('hidden');
                        } else {
                            moonIcon.classList.remove('hidden');
                            sunIcon.classList.add('hidden');
                        }
                        darkModeIcon.style.transform = 'rotate(0deg)';
                    }, 100);
                }

                // Update mobile icons
                if (mobileMoonIcon && mobileSunIcon && mobileDarkModeIcon) {
                    mobileDarkModeIcon.style.transform = 'rotate(180deg)';

                    setTimeout(() => {
                        if (isDark) {
                            mobileMoonIcon.classList.add('hidden');
                            mobileSunIcon.classList.remove('hidden');
                        } else {
                            mobileMoonIcon.classList.remove('hidden');
                            mobileSunIcon.classList.add('hidden');
                        }
                        mobileDarkModeIcon.style.transform = 'rotate(0deg)';
                    }, 100);
                }

                // Update mobile text
                if (mobileDarkModeText) {
                    mobileDarkModeText.textContent = isDark ? 'Light Mode' : 'Dark Mode';
                }
            }

            // Update icon on page load
            document.addEventListener('DOMContentLoaded', function() {
                const isDark = document.documentElement.classList.contains('dark');
                updateDarkModeIcon(isDark);
            });

            // Today's summary function for quick actions
            function loadTodaySummary() {
                fetch('{{ route("lunch.today-summary") }}')
                .then(response => response.json())
                .then(data => {
                    alert(`Today's Summary:\nOrdered: ${data.total_ordered}\nCancelled: ${data.total_cancelled}`);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while loading the summary.');
                });
            }
        </script>
    </body>
</html>
